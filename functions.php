<?php

function jn_enqueue_assets() {
    $scripts = array(
        'Jquery' => '/libs/jquery.min.js',
        'Lenis' => '/libs/lenis.min.js',
        'Swup' => '/libs/swup.js',
        'Swup_head' => '/libs/swup_head.js',
        'Swup_Gtag' => '/libs/swup_gtag.js',
        'Select2' => '/libs/select2.min.js',
        'GSAP' => '/libs/gsap.min.js',
        'Custom_Ease' => '/libs/CustomEase.min.js',
        'Splitlines' => '/libs/jquery.splitlines.js',
        'ScrollTrigger' => '/libs/ScrollTrigger.min.js',
        'Hammer_js' => '/libs/hammer.min.js',
        'main_js' => '/assets/js/main.js',
        'Header' => '/assets/js/header.js',
        'Workshop_Form' => '/assets/js/workshop-form.js'
    );

    foreach ($scripts as $handle => $path) {
        wp_enqueue_script($handle, get_theme_file_uri($path), array(), '1.0', true);
    }

    $blocks = array(
        'HOME HEADER BLOCK' => '/blocks/js/block-home-header-block.js',
        'REVIEW SLIDER BLOCK' => '/blocks/js/block-review-slider-block.js',
        'LAST UPDATES BLOCK' => '/blocks/js/block-last-updates-block.js',
        'ABOUT HEADER BLOCK' => '/blocks/js/block-about-header-block.js',
        'SERVICES HEADER BLOCK' => '/blocks/js/block-services-header-block.js',
        'UPDATES HEADER BLOCK' => '/blocks/js/block-update-header-block.js',
        'CASE HEADER BLOCK' => '/blocks/js/block-case-header-block.js',
        'UPDATES BLOCK' => '/blocks/js/block-updates-block.js',
        'CASES BLOCK' => '/blocks/js/block-cases-block.js',
        'PARTNERS SLIDER BLOCK' => '/blocks/js/block-partners-slider-block.js'
    );

    foreach ($blocks as $handle => $path) {
        wp_enqueue_script($handle, get_theme_file_uri($path), array(), '1.0', true);
    }

    wp_enqueue_style('main', get_stylesheet_uri());
    wp_enqueue_style('select2', get_theme_file_uri('/libs/select2.min.css'), array(), '1.1', 'all');
}

add_action('wp_enqueue_scripts', 'jn_enqueue_assets');

// Add favicon
function ilc_favicon() {
    echo "<link rel='shortcut icon' href='" . get_stylesheet_directory_uri() . "/favicon.ico' />\n";
}

add_action('wp_head', 'ilc_favicon');

// Customize theme settings
function jn_customize_register($wp_customize) {
    $sections = array(
        'customTheme-main-callout-title' => 'Title',
        'customTheme-main-callout-description' => 'Description',
        'customTheme-main-callout-featured-image' => 'Image',
        'customTheme-main-callout-logo' => 'Logo',
        'customTheme-main-callout-logo-white' => 'Logo (White)',
        'customTheme-main-callout-telephone' => 'Telephone',
        'customTheme-main-callout-telephone-label' => 'Telephone label',
        'customTheme-main-callout-mail' => 'Mail',
        'customTheme-main-callout-address' => 'Address',
        'customTheme-main-callout-facebook' => 'Facebook URL',
        'customTheme-main-callout-linkedin' => 'LinkedIn URL',
        'customTheme-main-callout-instagram' => 'Instagram URL',
        'customTheme-main-callout-analytics' => 'Analytics ID',
    );

    $wp_customize->add_section('customTheme-main-callout-section', array(
        'title' => 'Main Information'
    ));

    foreach ($sections as $setting_id => $label) {
        $wp_customize->add_setting($setting_id);
        $control_args = array(
            'label' => $label,
            'section' => 'customTheme-main-callout-section',
            'settings' => $setting_id
        );

        if (strpos($setting_id, 'featured-image') !== false || strpos($setting_id, 'logo') !== false) {
            $control_args['width'] = 750;
            $control_args['height'] = 500;
            $wp_customize->add_control(new WP_Customize_Media_Control($wp_customize, $setting_id . '-control', $control_args));
        } elseif ($label === 'Description') {
            $control_args['type'] = 'textarea';
            $wp_customize->add_control(new WP_Customize_Control($wp_customize, $setting_id . '-control', $control_args));
        } else {
            $wp_customize->add_control(new WP_Customize_Control($wp_customize, $setting_id . '-control', $control_args));
        }
    }
}

add_action('customize_register', 'jn_customize_register');

// Register menus
function jn_register_menus() {
    register_nav_menus(array(
        'primary-menu' => 'Primary Menu',
        'secondary-menu' => 'Secondary Menu',
        'primary-footer-menu' => 'Primary Footer Menu',
        'secondary-footer-menu' => 'Secondary Footer Menu',
        'third-footer-menu' => 'Third Footer Menu',
        'fourth-footer-menu' => 'Fourth Footer Menu',
        'fifth-footer-menu' => 'Fifth Footer Menu'
    ));
}

add_action('after_setup_theme', 'jn_register_menus');

// Remove max image preview setting
add_filter('wp_robots', 'remove_max_image_preview_large', 10, 1);
function remove_max_image_preview_large($robots) {
    unset($robots['max-image-preview']);
    return $robots;
}

// blocks

add_action('acf/init', 'my_acf_blocks_init');
function my_acf_blocks_init() {

    // Check function exists.
    if( function_exists('acf_register_block_type') ) {

        // Register a testimonial block.
        acf_register_block_type(array(
            'name'              => 'home_header_block',
            'title'             => __('Home header Block'),
            // 'description'       => __('A custom testimonial block.'),
            'render_template'   => 'blocks/home-header-block.php',
            'category'          => 'headers',
        ));
        acf_register_block_type(array(
            'name'              => 'about',
            'title'             => __('Home about Block'),
            // 'description'       => __('A custom testimonial block.'),
            'render_template'   => 'blocks/home-about-block.php',
            'category'          => 'about',
        ));
        acf_register_block_type(array(
            'name'              => 'review_slider_block',
            'title'             => __('Review slider block'),
            // 'description'       => __('A custom testimonial block.'),
            'render_template'   => 'blocks/review-slider-block.php',
            'category'          => 'reviews',
        ));
        acf_register_block_type(array(
            'name'              => 'call_to_actions_block',
            'title'             => __('Call to actions block'),
            // 'description'       => __('A custom testimonial block.'),
            'render_template'   => 'blocks/call-to-actions-block.php',
            'category'          => 'cta',
        ));
        acf_register_block_type(array(
            'name'              => 'last_updates_block',
            'title'             => __('Last updates block'),
            // 'description'       => __('A custom testimonial block.'),
            'render_template'   => 'blocks/last-updates-block.php',
            'category'          => 'updates',
        ));
        acf_register_block_type(array(
            'name'              => 'about_header_block',
            'title'             => __('About header block'),
            'render_template'   => 'blocks/about-header-block.php',
            'category'          => 'about',
        ));
        acf_register_block_type(array(
            'name'              => 'intro_text_block',
            'title'             => __('Intro text block'),
            'render_template'   => 'blocks/intro-text-block.php',
            'category'          => 'text',
        ));
        acf_register_block_type(array(
            'name'              => 'team_block',
            'title'             => __('Team block'),
            'render_template'   => 'blocks/team-block.php',
            'category'          => 'team',
        ));
        acf_register_block_type(array(
            'name'              => 'call_to_action_block',
            'title'             => __('Call to action block'),
            'render_template'   => 'blocks/call-to-action-block.php',
            'category'          => 'cta',
        ));
        acf_register_block_type(array(
            'name'              => 'update_header_block',
            'title'             => __('Update header block'),
            'render_template'   => 'blocks/update-header-block.php',
            'category'          => 'update',
        ));
        acf_register_block_type(array(
            'name'              => 'services_header_block',
            'title'             => __('Services header block'),
            'render_template'   => 'blocks/services-header-block.php',
            'category'          => 'services',
        ));
        acf_register_block_type(array(
            'name'              => 'image_text_block',
            'title'             => __('Image text block'),
            'render_template'   => 'blocks/image-text-block.php',
            'category'          => 'image',
        ));
        acf_register_block_type(array(
            'name'              => 'services_block',
            'title'             => __('Services block'),
            'render_template'   => 'blocks/services-block.php',
            'category'          => 'service',
        ));
        acf_register_block_type(array(
            'name'              => 'service_header_block',
            'title'             => __('Service header block'),
            'render_template'   => 'blocks/service-header-block.php',
            'category'          => 'service',
        ));
        acf_register_block_type(array(
            'name'              => 'downloads_block',
            'title'             => __('Downloads block'),
            'render_template'   => 'blocks/downloads-block.php',
            'category'          => 'downloads',
        ));
        acf_register_block_type(array(
            'name'              => 'members_by_amount_block',
            'title'             => __('Members by amount block'),
            'render_template'   => 'blocks/members-by-amount-block.php',
            'category'          => 'members,team',
        ));
        acf_register_block_type(array(
            'name'              => 'steps_block',
            'title'             => __('Steps block'),
            'render_template'   => 'blocks/steps-block.php',
            'category'          => 'steps',
        ));
        acf_register_block_type(array(
            'name'              => 'cases_block',
            'title'             => __('Cases block'),
            'render_template'   => 'blocks/cases-block.php',
            'category'          => 'cases',
        ));
        acf_register_block_type(array(
            'name'              => 'case_header_block',
            'title'             => __('Case header block'),
            'render_template'   => 'blocks/case-header-block.php',
            'category'          => 'cases',
        ));
        acf_register_block_type(array(
            'name'              => 'list_block',
            'title'             => __('List block'),
            'render_template'   => 'blocks/list-block.php',
            'category'          => 'list',
        ));
        acf_register_block_type(array(
            'name'              => 'two_images_block',
            'title'             => __('Two Images block'),
            'render_template'   => 'blocks/two-images-block.php',
            'category'          => 'images',
        ));
        acf_register_block_type(array(
            'name'              => 'case_footer_block',
            'title'             => __('Case footer block'),
            'render_template'   => 'blocks/case-footer-block.php',
            'category'          => 'cases',
        ));
        acf_register_block_type(array(
            'name'              => 'header_block',
            'title'             => __('Header block'),
            'render_template'   => 'blocks/header-block.php',
            'category'          => 'header',
        ));
        acf_register_block_type(array(
            'name'              => 'big_image_block',
            'title'             => __('Big Image block'),
            'render_template'   => 'blocks/big-image-block.php',
            'category'          => 'image',
        ));
        acf_register_block_type(array(
            'name'              => 'vacature_header_block',
            'title'             => __('Vacature Header block'),
            'render_template'   => 'blocks/vacature-header-block.php',
            'category'          => 'vacature header block',
        ));
        acf_register_block_type(array(
            'name'              => 'updates_block',
            'title'             => __('Updates block'),
            'render_template'   => 'blocks/updates-block.php',
            'category'          => 'Updates block',
        ));
        acf_register_block_type(array(
            'name'              => 'contact_block',
            'title'             => __('Contact block'),
            'render_template'   => 'blocks/contact-block.php',
            'category'          => 'Contact block',
        ));
        acf_register_block_type(array(
            'name'              => 'vacatures_block',
            'title'             => __('Vacatures block'),
            'render_template'   => 'blocks/vacatures-block.php',
            'category'          => 'Vacatures block',
        ));
        acf_register_block_type(array(
            'name'              => 'partners_slider_block',
            'title'             => __('Partners slider block'),
            'render_template'   => 'blocks/partners-slider-block.php',
            'category'          => 'Partners slider block',
        ));
        acf_register_block_type(array(
            'name'              => 'latest_cases_block',
            'title'             => __('Latest cases block'),
            'render_template'   => 'blocks/latest-cases-block.php',
            'category'          => 'Latest cases block',
        ));
        acf_register_block_type(array(
            'name'              => 'companions_block',
            'title'             => __('Companions block'),
            'render_template'   => 'blocks/companions-block.php',
            'category'          => 'Companions block',
        ));
    }
}

// Workshop functionaliteit
// Registreer workshop post type
function register_workshop_post_type() {
    $labels = array(
        'name'                  => 'Workshops',
        'singular_name'         => 'Workshop',
        'menu_name'             => 'Workshops',
        'name_admin_bar'        => 'Workshop',
        'archives'              => 'Workshop Archives',
        'attributes'            => 'Workshop Attributes',
        'parent_item_colon'     => 'Parent Workshop:',
        'all_items'             => 'All Workshops',
        'add_new_item'          => 'Add New Workshop',
        'add_new'               => 'Add New',
        'new_item'              => 'New Workshop',
        'edit_item'             => 'Edit Workshop',
        'update_item'           => 'Update Workshop',
        'view_item'             => 'View Workshop',
        'view_items'            => 'View Workshops',
        'search_items'          => 'Search Workshop',
        'not_found'             => 'Not found',
        'not_found_in_trash'    => 'Not found in Trash',
        'featured_image'        => 'Featured Image',
        'set_featured_image'    => 'Set featured image',
        'remove_featured_image' => 'Remove featured image',
        'use_featured_image'    => 'Use as featured image',
        'insert_into_item'      => 'Insert into workshop',
        'uploaded_to_this_item' => 'Uploaded to this workshop',
        'items_list'            => 'Workshops list',
        'items_list_navigation' => 'Workshops list navigation',
        'filter_items_list'     => 'Filter workshops list',
    );

    $args = array(
        'label'                 => 'Workshop',
        'description'           => 'Workshop posts',
        'labels'                => $labels,
        'supports'              => array('title', 'editor', 'thumbnail', 'custom-fields'),
        'hierarchical'          => false,
        'public'                => true,
        'show_ui'               => true,
        'show_in_menu'          => true,
        'menu_position'         => 5,
        'menu_icon'             => 'dashicons-calendar-alt',
        'show_in_admin_bar'     => true,
        'show_in_nav_menus'     => true,
        'can_export'            => true,
        'has_archive'           => true,
        'exclude_from_search'   => false,
        'publicly_queryable'    => true,
        'capability_type'       => 'post',
        'show_in_rest'          => true,
    );

    register_post_type('workshop', $args);
}
add_action('init', 'register_workshop_post_type', 0);

// Workshop options shortcode voor Contact Form 7
function workshop_options_shortcode($atts) {
    $atts = shortcode_atts(array(
        'type' => 'select', // select of radio
        'empty_option' => 'Selecteer een workshop...',
        'show_date' => true,
        'show_price' => false,
    ), $atts);

    // Haal alle workshops op
    $workshops = get_posts(array(
        'post_type' => 'workshop',
        'posts_per_page' => -1,
        'post_status' => 'publish',
        'orderby' => 'date',
        'order' => 'ASC',
        'meta_query' => array(
            array(
                'key' => 'workshop_date',
                'value' => date('Y-m-d'),
                'compare' => '>=',
                'type' => 'DATE'
            )
        )
    ));

    if (empty($workshops)) {
        return '<option value="">Geen workshops beschikbaar</option>';
    }

    $output = '';

    if ($atts['type'] === 'select') {
        if (!empty($atts['empty_option'])) {
            $output .= '<option value="">' . esc_html($atts['empty_option']) . '</option>';
        }

        foreach ($workshops as $workshop) {
            $title = get_the_title($workshop->ID);
            $date = get_field('workshop_date', $workshop->ID);
            $price = get_field('workshop_price', $workshop->ID);

            $option_text = $title;
            if ($atts['show_date'] && $date) {
                $formatted_date = date('d-m-Y', strtotime($date));
                $option_text .= ' (' . $formatted_date . ')';
            }
            if ($atts['show_price'] && $price) {
                $option_text .= ' - €' . $price;
            }

            $output .= '<option value="' . esc_attr($title) . '">' . esc_html($option_text) . '</option>';
        }
    } else {
        // Radio buttons
        foreach ($workshops as $workshop) {
            $title = get_the_title($workshop->ID);
            $date = get_field('workshop_date', $workshop->ID);
            $price = get_field('workshop_price', $workshop->ID);

            $option_text = $title;
            if ($atts['show_date'] && $date) {
                $formatted_date = date('d-m-Y', strtotime($date));
                $option_text .= ' (' . $formatted_date . ')';
            }
            if ($atts['show_price'] && $price) {
                $option_text .= ' - €' . $price;
            }

            $output .= '<label><input type="radio" name="workshop" value="' . esc_attr($title) . '"> ' . esc_html($option_text) . '</label><br>';
        }
    }

    return $output;
}
add_shortcode('workshop_options', 'workshop_options_shortcode');

// Contact Form 7 workshop field handler
function add_workshop_form_tag() {
    wpcf7_add_form_tag('workshop', 'workshop_form_tag_handler', array('name-attr' => true));
    wpcf7_add_form_tag('workshop*', 'workshop_form_tag_handler', array('name-attr' => true));
}
add_action('wpcf7_init', 'add_workshop_form_tag');

function workshop_form_tag_handler($tag) {
    if (empty($tag->name)) {
        return '';
    }

    $validation_error = wpcf7_get_validation_error($tag->name);
    $class = wpcf7_form_controls_class($tag->type, 'wpcf7-select');

    if ($validation_error) {
        $class .= ' wpcf7-not-valid';
    }

    $atts = array();
    $atts['class'] = $class;
    $atts['id'] = $tag->get_id_option();
    $atts['tabindex'] = $tag->get_option('tabindex', 'signed_int', true);
    $atts['name'] = $tag->name;

    if ($tag->is_required()) {
        $atts['aria-required'] = 'true';
    }

    $atts['aria-invalid'] = $validation_error ? 'true' : 'false';
    $atts = wpcf7_format_atts($atts);

    // Haal workshops op
    $workshops = get_posts(array(
        'post_type' => 'workshop',
        'posts_per_page' => -1,
        'post_status' => 'publish',
        'orderby' => 'date',
        'order' => 'ASC',
        'meta_query' => array(
            array(
                'key' => 'workshop_date',
                'value' => date('Y-m-d'),
                'compare' => '>=',
                'type' => 'DATE'
            )
        )
    ));

    $html = sprintf('<select %s>', $atts);
    $html .= '<option value="">Selecteer een workshop...</option>';

    foreach ($workshops as $workshop) {
        $title = get_the_title($workshop->ID);
        $date = get_field('workshop_date', $workshop->ID);

        $option_text = $title;
        if ($date) {
            $formatted_date = date('d-m-Y', strtotime($date));
            $option_text .= ' (' . $formatted_date . ')';
        }

        $html .= sprintf('<option value="%s">%s</option>',
            esc_attr($title),
            esc_html($option_text)
        );
    }

    $html .= '</select>';
    $html .= $validation_error;

    return $html;
}

// Validatie voor workshop field
function workshop_validation_filter($result, $tag) {
    if ('workshop' == $tag->type || 'workshop*' == $tag->type) {
        $name = $tag->name;
        $value = isset($_POST[$name]) ? trim($_POST[$name]) : '';

        if ($tag->is_required() && empty($value)) {
            $result->invalidate($tag, wpcf7_get_message('invalid_required'));
        }
    }

    return $result;
}
add_filter('wpcf7_validate_workshop', 'workshop_validation_filter', 10, 2);
add_filter('wpcf7_validate_workshop*', 'workshop_validation_filter', 10, 2);

// Verbeter Contact Form 7 mail templates
function improve_cf7_mail_template($components, $contact_form, $mail) {
    // Controleer of er een workshop veld is
    $submission = WPCF7_Submission::get_instance();
    if (!$submission) {
        return $components;
    }

    $posted_data = $submission->get_posted_data();

    // Zoek naar workshop data
    $workshop_value = '';
    foreach ($posted_data as $key => $value) {
        if (strpos($key, 'workshop') !== false && !empty($value)) {
            $workshop_value = $value;
            break;
        }
    }

    if (!empty($workshop_value)) {
        // Vervang lege "Workshop:" in de mail body
        $body = $components['body'];

        // Verschillende patronen om te vervangen
        $patterns = array(
            '/Workshop:\s*$/m',
            '/Workshop:\s*\n/m',
            '/Workshop:\s*\r\n/m',
            '/Workshop:\s*<br\s*\/?>/i'
        );

        foreach ($patterns as $pattern) {
            $body = preg_replace($pattern, 'Workshop: ' . $workshop_value, $body);
        }

        // Als er geen "Workshop:" gevonden wordt, voeg het toe
        if (strpos($body, 'Workshop:') === false) {
            // Voeg workshop info toe na de naam/email sectie
            $body = preg_replace(
                '/(E-mailadres:.*?(?:\n|\r\n))/s',
                '$1' . "\n" . 'Workshop: ' . $workshop_value . "\n",
                $body
            );
        }

        $components['body'] = $body;
    }

    return $components;
}
add_filter('wpcf7_mail_components', 'improve_cf7_mail_template', 10, 3);

// Voeg workshop mail tag toe
function add_workshop_mail_tag($output, $name, $html) {
    if ($name === 'workshop') {
        $submission = WPCF7_Submission::get_instance();
        if ($submission) {
            $posted_data = $submission->get_posted_data();

            // Zoek naar workshop data
            foreach ($posted_data as $key => $value) {
                if (strpos($key, 'workshop') !== false && !empty($value)) {
                    return $value;
                }
            }
        }
    }

    return $output;
}
add_filter('wpcf7_special_mail_tags', 'add_workshop_mail_tag', 10, 3);

// Verbeter de bevestigingsmail met workshop details
function enhance_workshop_confirmation_mail($contact_form) {
    // Haal de mail templates op
    $mail = $contact_form->prop('mail');
    $mail_2 = $contact_form->prop('mail_2');

    // Controleer of er workshop velden zijn in het formulier
    $form_content = $contact_form->prop('form');
    if (strpos($form_content, 'workshop') !== false) {

        // Verbeter de hoofdmail template
        if (!empty($mail['body'])) {
            $body = $mail['body'];

            // Zorg ervoor dat [workshop] tag wordt gebruikt
            if (strpos($body, '[workshop]') === false) {
                // Voeg workshop tag toe als deze ontbreekt
                $body = str_replace(
                    'Workshop:',
                    'Workshop: [workshop]',
                    $body
                );

                $mail['body'] = $body;
                $contact_form->set_properties(array('mail' => $mail));
            }
        }

        // Verbeter de autoresponder mail als deze bestaat
        if (!empty($mail_2['body'])) {
            $body_2 = $mail_2['body'];

            if (strpos($body_2, '[workshop]') === false) {
                $body_2 = str_replace(
                    'Workshop:',
                    'Workshop: [workshop]',
                    $body_2
                );

                $mail_2['body'] = $body_2;
                $contact_form->set_properties(array('mail_2' => $mail_2));
            }
        }
    }
}
add_action('wpcf7_before_send_mail', 'enhance_workshop_confirmation_mail');

// Zorg ervoor dat workshop data correct wordt opgeslagen
function save_workshop_submission_data($contact_form) {
    $submission = WPCF7_Submission::get_instance();
    if (!$submission) {
        return;
    }

    $posted_data = $submission->get_posted_data();

    // Zoek naar workshop data en sla deze op in de sessie voor gebruik in mail
    foreach ($posted_data as $key => $value) {
        if (strpos($key, 'workshop') !== false && !empty($value)) {
            // Sla workshop data op in een transient voor gebruik in mail
            set_transient('cf7_workshop_' . $submission->get_meta('remote_ip'), $value, 300); // 5 minuten
            break;
        }
    }
}
add_action('wpcf7_before_send_mail', 'save_workshop_submission_data', 5);

?>
