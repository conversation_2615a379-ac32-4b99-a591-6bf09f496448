# Workshop Registratie Test Instructies

## Overzicht van de Oplossing

Het workshop registratie probleem is opgelost door:

1. **Workshop Post Type**: <PERSON><PERSON> nieuw custom post type 'workshop' toegevoegd
2. **Workshop Options Shortcode**: `[workshop_options]` shortcode geïmplementeerd
3. **Contact Form 7 Integration**: Custom `[workshop]` field type toegevoegd
4. **Mail Template Verbetering**: Automatische workshop naam invulling in bevestigingsmails
5. **JavaScript Enhancements**: Verbeterde gebruikerservaring met jQuery
6. **CSS Styling**: Professionele styling voor workshop selectie

## Stap 1: Test Data Installeren

1. **Database Setup**:
   ```bash
   # Voer het SQL script uit in je WordPress database
   mysql -u [username] -p [database_name] < workshop-test-data.sql
   ```

2. **Alternatief via WordPress Admin**:
   - Ga naar WordPress Admin → Workshops
   - Maak handmatig enkele test workshops aan
   - Voeg de volgende ACF velden toe:
     - `workshop_date` (Date field)
     - `workshop_price` (Number field)
     - `workshop_duration` (Text field)
     - `workshop_location` (Text field)

## Stap 2: Contact Form 7 Formulier Updaten

### Optie A: Gebruik de nieuwe [workshop] field type
```
<div class="field half">
<label>Naam </label>[text* your-name autocomplete:name]
</div>
<div class="field half">
<label>E-mailadres</label>[email* your-email autocomplete:email]
</div>

<div class="field">
<label>Kies een workshop</label>
[workshop* workshop]
</div>

<div class="field submit"><div class="textLink"><i class="icon-arrow-right"></i>[submit "Versturen" class:textLink]<i class="icon-arrow-right"></i></div>
</div>
```

### Optie B: Gebruik de [workshop_options] shortcode
```
<div class="field half">
<label>Naam </label>[text* your-name autocomplete:name]
</div>
<div class="field half">
<label>E-mailadres</label>[email* your-email autocomplete:email]
</div>

<div class="field">
<label>Kies een workshop</label>
[select* workshop "[workshop_options]"]
</div>

<div class="field submit"><div class="textLink"><i class="icon-arrow-right"></i>[submit "Versturen" class:textLink]<i class="icon-arrow-right"></i></div>
</div>
```

## Stap 3: Mail Template Configureren

### Mail Body Template:
```
Beste [your-name],

Bedankt voor je inschrijving bij Soilmania Academy!
We hebben je aanmelding voor de volgende workshop ontvangen:

Workshop: [workshop]

Je ontvangt binnenkort verdere informatie per e-mail.

Met vriendelijke groet,
Team Soilmania
```

### Mail 2 (Auto-responder) Template:
```
Dear [your-name],

Thank you for registering for Soilmania Academy!
We have received your registration for the following workshop:

Workshop: [workshop]

You will receive more information by email shortly.

Kind regards,
Team Soilmania
```

## Stap 4: Test Scenario's

### Test 1: Basis Functionaliteit
1. Ga naar de pagina met het workshop registratie formulier
2. Controleer of de workshop dropdown wordt geladen met beschikbare workshops
3. Selecteer een workshop
4. Vul naam en email in
5. Verstuur het formulier
6. Controleer of de bevestigingsmail de juiste workshop naam bevat

### Test 2: Validatie
1. Probeer het formulier te versturen zonder workshop selectie
2. Controleer of er een error message verschijnt
3. Controleer of het formulier niet wordt verstuurd

### Test 3: Gebruikerservaring
1. Test de Select2 dropdown functionaliteit
2. Controleer de loading state tijdens form submit
3. Test de auto-save functionaliteit (refresh de pagina)
4. Controleer de responsive styling op mobiel

### Test 4: Mail Template
1. Verstuur een test registratie
2. Controleer beide mail templates (admin en auto-responder)
3. Verificeer dat "Workshop: [workshop naam]" correct wordt ingevuld
4. Test met verschillende workshops

## Stap 5: Troubleshooting

### Probleem: Workshop dropdown is leeg
**Oplossing**: 
- Controleer of er workshops zijn aangemaakt in WordPress Admin
- Controleer of workshops de status 'publish' hebben
- Controleer of workshop_date veld is ingevuld met toekomstige datum

### Probleem: Workshop naam niet in mail
**Oplossing**:
- Controleer of `[workshop]` tag wordt gebruikt in mail template
- Controleer of het form field de naam 'workshop' heeft
- Clear cache en test opnieuw

### Probleem: JavaScript errors
**Oplossing**:
- Controleer browser console voor errors
- Zorg ervoor dat jQuery en Select2 correct worden geladen
- Controleer of workshop-form.js wordt geladen

### Probleem: Styling issues
**Oplossing**:
- Compileer LESS files opnieuw
- Controleer of workshop-form.less wordt geïmporteerd
- Clear browser cache

## Stap 6: Productie Deployment

1. **Backup maken** van database en bestanden
2. **Test eerst** op staging environment
3. **Upload bestanden**:
   - functions.php
   - assets/js/workshop-form.js
   - assets/less/workshop-form.less
   - assets/less/style.less (updated)
4. **Compileer CSS** als nodig
5. **Clear cache** (WordPress + browser)
6. **Test alle functionaliteit** op live site

## Extra Features

### Workshop Details Tooltip
De JavaScript voegt automatisch tooltips toe die de geselecteerde workshop bevestigen.

### Auto-save Functionaliteit
Form data wordt automatisch opgeslagen in localStorage en hersteld bij page refresh.

### Analytics Tracking
Google Analytics events worden automatisch getrackt voor workshop selecties en registraties.

### Accessibility
ARIA labels en keyboard navigation zijn geïmplementeerd voor betere toegankelijkheid.

## Support

Voor vragen of problemen:
1. Controleer eerst de troubleshooting sectie
2. Check browser console voor JavaScript errors
3. Controleer WordPress error logs
4. Test met verschillende browsers en devices
