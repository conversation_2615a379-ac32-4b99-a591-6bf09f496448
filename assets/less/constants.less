@primaryColor: #133535;
@primaryColorDark: #133535;
@secondaryColor: #9ECEA2;
@secondaryColorLight: #9ECEA2;
@thirdColor: #42AF60;
@almostBlack: #1D1D1B;
@hardBlack: #151515;
@hardWhite: #FFFFFF;
@grey: #B4B4B4;
@lighterGrey;
@darkerGrey: #464646;
@lightGrey: #999999;
@borderGrey: #F5F5F5;
@almostWhite: #EDEDED;

@yellow: #D8A104;
@pink: #ED278F;
@green: #3FA745;
@blue: #3A2AF5;

@backgroundGrey: #F5F5F5;

// 1728
@vw1474: 85.301vw;
@vw1316: 76.157vw;
@vw1305: 75.521vw;
@vw1200: 69.444vw;
@vw950: 54.977vw;
@vw946: 54.745vw;
@vw812: 46.991vw;
@vw770: 44.560vw;
@vw748: 43.287vw;
@vw744: 43.056vw;
@vw674: 39.005vw;
@vw650: 37.616vw;
@vw594: 34.375vw;
@vw544: 31.481vw;
@vw536: 31.019vw;
@vw428: 24.769vw;
@vw398: 23.032vw;
@vw336: 19.444vw;
@vw276: 15.972vw;
@vw222: 12.847vw;
@vw192: 11.111vw;
@vw175: 10.127vw;
@vw134: 7.755vw;
@vw120: 6.944vw;
@vw114: 6.597vw;
@vw112: 6.481vw;
@vw104: 6.019vw;
@vw100: 5.787vw;
@vw99: 5.729vw;
@vw98: 5.671vw;
@vw97: 5.613vw;
@vw96: 5.556vw;
@vw95: 5.498vw;
@vw94: 5.440vw;
@vw93: 5.382vw;
@vw92: 5.324vw;
@vw91: 5.266vw;
@vw90: 5.208vw;
@vw89: 5.150vw;
@vw88: 5.093vw;
@vw87: 5.035vw;
@vw86: 4.977vw;
@vw85: 4.919vw;
@vw84: 4.861vw;
@vw83: 4.803vw;
@vw82: 4.745vw;
@vw81: 4.687vw;
@vw80: 4.630vw;
@vw79: 4.572vw;
@vw78: 4.514vw;
@vw77: 4.456vw;
@vw76: 4.398vw;
@vw75: 4.340vw;
@vw74: 4.282vw;
@vw73: 4.224vw;
@vw72: 4.167vw;
@vw71: 4.109vw;
@vw70: 4.051vw;
@vw69: 3.993vw;
@vw68: 3.935vw;
@vw67: 3.877vw;
@vw66: 3.819vw;
@vw65: 3.761vw;
@vw64: 3.704vw;
@vw63: 3.646vw;
@vw62: 3.588vw;
@vw61: 3.530vw;
@vw60: 3.472vw;
@vw59: 3.414vw;
@vw58: 3.356vw;
@vw57: 3.298vw;
@vw56: 3.241vw;
@vw55: 3.183vw;
@vw54: 3.125vw;
@vw53: 3.067vw;
@vw52: 3.009vw;
@vw51: 2.951vw;
@vw50: 2.894vw;
@vw49: 2.836vw;
@vw48: 2.778vw;
@vw47: 2.720vw;
@vw46: 2.662vw;
@vw45: 2.604vw;
@vw44: 2.546vw;
@vw43: 2.488vw;
@vw42: 2.431vw;
@vw41: 2.373vw;
@vw40: 2.315vw;
@vw39: 2.257vw;
@vw38: 2.199vw;
@vw37: 2.141vw;
@vw36: 2.083vw;
@vw35: 2.025vw;
@vw34: 1.968vw;
@vw33: 1.910vw;
@vw32: 1.852vw;
@vw31: 1.794vw;
@vw30: 1.736vw;
@vw29: 1.678vw;
@vw28: 1.620vw;
@vw27: 1.563vw;
@vw26: 1.505vw;
@vw25: 1.447vw;
@vw24: 1.389vw;
@vw23: 1.331vw;
@vw22: 1.273vw;
@vw21: 1.215vw;
@vw20: 1.157vw;
@vw19: 1.099vw;
@vw18: 1.042vw;
@vw17: 0.984vw;
@vw16: 0.926vw;
@vw15: 0.868vw;
@vw14: 0.810vw;
@vw13: 0.752vw;
@vw12: 0.694vw;
@vw11: 0.636vw;
@vw10: 0.579vw;
@vw9: 0.521vw;
@vw8: 0.463vw;
@vw7: 0.405vw;
@vw6: 0.347vw;
@vw5: 0.289vw;
@vw4: 0.231vw;
@vw3: 0.174vw;
@vw2: 0.116vw;
@vw1: 0.058vw;


// 1160 tablet
@vw1474-1160: 100vw;
@vw1316-1160: 100vw;
@vw1305-1160: 100vw;
@vw1200-1160: 103.448vw; /* Oorspronkelijke waarde is meer dan 100vw */
@vw950-1160: 81.034vw;
@vw946-1160: 81.552vw;
@vw812-1160: 69.897vw;
@vw770-1160: 66.379vw;
@vw748-1160: 64.483vw;
@vw744-1160: 64.207vw;
@vw674-1160: 58.103vw;
@vw650-1160: 55.172vw;
@vw594-1160: 51.379vw;
@vw544-1160: 46.897vw;
@vw536-1160: 46.379vw;
@vw428-1160: 36.552vw;
@vw398-1160: 34.379vw;
@vw336-1160: 28.966vw;
@vw276-1160: 23.759vw;
@vw222-1160: 19.138vw;
@vw192-1160: 16.552vw;
@vw175-1160: 15.086vw;
@vw134-1160: 11.552vw;
@vw120-1160: 10.345vw;
@vw114-1160: 9.827vw;
@vw112-1160: 9.655vw;
@vw104-1160: 8.965vw;
@vw100-1160: 8.621vw;
@vw99-1160: 8.534vw;
@vw98-1160: 8.448vw;
@vw97-1160: 8.362vw;
@vw96-1160: 8.276vw;
@vw95-1160: 8.190vw;
@vw94-1160: 8.103vw;
@vw93-1160: 8.017vw;
@vw92-1160: 7.931vw;
@vw91-1160: 7.845vw;
@vw90-1160: 7.759vw;
@vw89-1160: 7.672vw;
@vw88-1160: 7.586vw;
@vw87-1160: 7.500vw;
@vw86-1160: 7.414vw;
@vw85-1160: 7.328vw;
@vw84-1160: 7.241vw;
@vw83-1160: 7.155vw;
@vw82-1160: 7.069vw;
@vw81-1160: 6.983vw;
@vw80-1160: 6.897vw;
@vw79-1160: 6.810vw;
@vw78-1160: 6.724vw;
@vw77-1160: 6.638vw;
@vw76-1160: 6.552vw;
@vw75-1160: 6.466vw;
@vw74-1160: 6.379vw;
@vw73-1160: 6.293vw;
@vw72-1160: 6.207vw;
@vw71-1160: 6.121vw;
@vw70-1160: 6.034vw;
@vw69-1160: 5.948vw;
@vw68-1160: 5.862vw;
@vw67-1160: 5.776vw;
@vw66-1160: 5.690vw;
@vw65-1160: 5.603vw;
@vw64-1160: 5.517vw;
@vw63-1160: 5.431vw;
@vw62-1160: 5.345vw;
@vw61-1160: 5.259vw;
@vw60-1160: 5.172vw;
@vw59-1160: 5.086vw;
@vw58-1160: 5.000vw;
@vw57-1160: 4.914vw;
@vw56-1160: 4.828vw;
@vw55-1160: 4.741vw;
@vw54-1160: 4.655vw;
@vw53-1160: 4.569vw;
@vw52-1160: 4.483vw;
@vw51-1160: 4.397vw;
@vw50-1160: 4.310vw;
@vw49-1160: 4.224vw;
@vw48-1160: 4.138vw;
@vw47-1160: 4.052vw;
@vw46-1160: 3.966vw;
@vw45-1160: 3.879vw;
@vw44-1160: 3.793vw;
@vw43-1160: 3.707vw;
@vw42-1160: 3.621vw;
@vw41-1160: 3.534vw;
@vw40-1160: 3.448vw;
@vw39-1160: 3.362vw;
@vw38-1160: 3.276vw;
@vw37-1160: 3.190vw;
@vw36-1160: 3.103vw;
@vw35-1160: 3.017vw;
@vw34-1160: 2.931vw;
@vw33-1160: 2.845vw;
@vw32-1160: 2.759vw;
@vw31-1160: 2.672vw;
@vw30-1160: 2.586vw;
@vw29-1160: 2.500vw;
@vw28-1160: 2.414vw;
@vw27-1160: 2.328vw;
@vw26-1160: 2.241vw;
@vw25-1160: 2.155vw;
@vw24-1160: 2.069vw;
@vw23-1160: 1.983vw;
@vw22-1160: 1.897vw;
@vw21-1160: 1.810vw;
@vw20-1160: 1.724vw;
@vw19-1160: 1.638vw;
@vw18-1160: 1.552vw;
@vw17-1160: 1.466vw;
@vw16-1160: 1.379vw;
@vw15-1160: 1.293vw;
@vw14-1160: 1.207vw;
@vw13-1160: 1.121vw;
@vw12-1160: 1.034vw;
@vw11-1160: 0.948vw;
@vw10-1160: 0.862vw;
@vw9-1160: 0.776vw;
@vw8-1160: 0.690vw;
@vw7-1160: 0.603vw;
@vw6-1160: 0.517vw;
@vw5-1160: 0.431vw;
@vw4-1160: 0.345vw;
@vw3-1160: 0.259vw;
@vw2-1160: 0.172vw;
@vw1-1160: 0.086vw;

// 580 mobile
@vw1474-580: 254.482vw;
@vw1316-580: 226.897vw;
@vw1305-580: 224.310vw;
@vw1200-580: 206.897vw;
@vw950-580: 163.793vw;
@vw946-580: 162.414vw;
@vw812-580: 140.690vw;
@vw770-580: 132.759vw;
@vw748-580: 128.897vw;
@vw744-580: 128.414vw;
@vw674-580: 116.897vw;
@vw650-580: 112.069vw;
@vw594-580: 102.414vw;
@vw544-580: 93.448vw;
@vw536-580: 92.759vw;
@vw428-580: 73.103vw;
@vw398-580: 68.483vw;
@vw336-580: 57.241vw;
@vw276-580: 47.241vw;
@vw222-580: 38.379vw;
@vw192-580: 33.241vw;
@vw175-580: 30.172vw;
@vw134-580: 23.103vw;
@vw120-580: 20.690vw;
@vw114-580: 19.655vw;
@vw112-580: 19.310vw;
@vw104-580: 17.931vw;
@vw100-580: 17.241vw;
@vw99-580: 17.069vw;
@vw98-580: 16.897vw;
@vw97-580: 16.724vw;
@vw96-580: 16.552vw;
@vw95-580: 16.379vw;
@vw94-580: 16.207vw;
@vw93-580: 16.034vw;
@vw92-580: 15.862vw;
@vw91-580: 15.690vw;
@vw90-580: 15.517vw;
@vw89-580: 15.345vw;
@vw88-580: 15.172vw;
@vw87-580: 15.000vw;
@vw86-580: 14.828vw;
@vw85-580: 14.655vw;
@vw84-580: 14.483vw;
@vw83-580: 14.310vw;
@vw82-580: 14.138vw;
@vw81-580: 13.966vw;
@vw80-580: 13.793vw;
@vw79-580: 13.621vw;
@vw78-580: 13.448vw;
@vw77-580: 13.276vw;
@vw76-580: 13.103vw;
@vw75-580: 12.931vw;
@vw74-580: 12.759vw;
@vw73-580: 12.586vw;
@vw72-580: 12.414vw;
@vw71-580: 12.241vw;
@vw70-580: 12.069vw;
@vw69-580: 11.897vw;
@vw68-580: 11.724vw;
@vw67-580: 11.552vw;
@vw66-580: 11.379vw;
@vw65-580: 11.207vw;
@vw64-580: 11.034vw;
@vw63-580: 10.862vw;
@vw62-580: 10.690vw;
@vw61-580: 10.517vw;
@vw60-580: 10.345vw;
@vw59-580: 10.172vw;
@vw58-580: 10.000vw;
@vw57-580: 9.828vw;
@vw56-580: 9.655vw;
@vw55-580: 9.483vw;
@vw54-580: 9.310vw;
@vw53-580: 9.138vw;
@vw52-580: 8.965vw;
@vw51-580: 8.793vw;
@vw50-580: 8.621vw;
@vw49-580: 8.448vw;
@vw48-580: 8.276vw;
@vw47-580: 8.103vw;
@vw46-580: 7.931vw;
@vw45-580: 7.759vw;
@vw44-580: 7.586vw;
@vw43-580: 7.414vw;
@vw42-580: 7.241vw;
@vw41-580: 7.069vw;
@vw40-580: 6.897vw;
@vw39-580: 6.724vw;
@vw38-580: 6.552vw;
@vw37-580: 6.379vw;
@vw36-580: 6.207vw;
@vw35-580: 6.034vw;
@vw34-580: 5.862vw;
@vw33-580: 5.690vw;
@vw32-580: 5.517vw;
@vw31-580: 5.345vw;
@vw30-580: 5.172vw;
@vw29-580: 5.000vw;
@vw28-580: 4.828vw;
@vw27-580: 4.655vw;
@vw26-580: 4.483vw;
@vw25-580: 4.310vw;
@vw24-580: 4.138vw;
@vw23-580: 3.966vw;
@vw22-580: 3.793vw;
@vw21-580: 3.621vw;
@vw20-580: 3.448vw;
@vw19-580: 3.276vw;
@vw18-580: 3.103vw;
@vw17-580: 2.931vw;
@vw16-580: 2.759vw;
@vw15-580: 2.586vw;
@vw14-580: 2.414vw;
@vw13-580: 2.241vw;
@vw12-580: 2.069vw;
@vw11-580: 1.897vw;
@vw10-580: 1.724vw;
@vw9-580: 1.552vw;
@vw8-580: 1.379vw;
@vw7-580: 1.207vw;
@vw6-580: 1.034vw;
@vw5-580: 0.862vw;
@vw4-580: 0.690vw;
@vw3-580: 0.517vw;
@vw2-580: 0.345vw;
@vw1-580: 0.172vw;


// font sizes
@fsHuge: @vw100;
@fsBig: @vw52;
@fsNormal: @vw32;
@fsDefault: @vw21;

// line heights
@lhHuge: @vw100;
@lhBig: @vw63;
@lhNormal: @vw39;
@lhDefault: @vw32;

.transition(@duration:0.2s, @ease:ease-out) {
    -webkit-transition: all @duration @ease;
    -moz-transition: all @duration @ease;
    -o-transition: all @duration @ease;
    transition: all @duration @ease;
}

.transitionMore(@what:all, @duration:0.2s, @delay:0s, @ease:ease-out) {
    -webkit-transition: @what @duration @delay @ease;
    -moz-transition: @what @duration @delay @ease;
    -o-transition: @what @duration @delay @ease;
    transition: @what @duration @delay @ease;
}
