// out: ../../style.css, compress: true, strictMath: true

/*
Theme Name: SOILMANIA
Author: themanis_dennis
Version: 1.0.0
*/

@import 'constants.less';
@import 'default.less';
@import 'parts/header.less';
@import 'parts/footer.less';
@import 'parts/loader.less';
@import 'parts/fonts.less';

// blocks
@import '../../blocks/less/block-home-header-block.less';
@import '../../blocks/less/block-home-about-block.less';
@import '../../blocks/less/block-review-slider-block.less';
@import '../../blocks/less/call-to-actions-block.less';
@import '../../blocks/less/call-to-action-block.less';
@import '../../blocks/less/last-updates-block.less';
@import '../../blocks/less/about-header-block.less';
@import '../../blocks/less/intro-text-block.less';
@import '../../blocks/less/team-block.less';
@import '../../blocks/less/update-header-block.less';
@import '../../blocks/less/services-header-block.less';
@import '../../blocks/less/image-text-block.less';
@import '../../blocks/less/services-block.less';
@import '../../blocks/less/service-header-block.less';
@import '../../blocks/less/downloads-block.less';
@import '../../blocks/less/steps-block.less';
@import '../../blocks/less/cases-block.less';
@import '../../blocks/less/case-header-block.less';
@import '../../blocks/less/list-block.less';
@import '../../blocks/less/two-images-block.less';
@import '../../blocks/less/case-footer-block.less';
@import '../../blocks/less/header-block.less';
@import '../../blocks/less/big-image-block.less';
@import '../../blocks/less/vacature-header-block.less';
@import '../../blocks/less/updates-block.less';
@import '../../blocks/less/contact-block.less';
@import '../../blocks/less/vacatures-block.less';
@import '../../blocks/less/partners-slider-block.less';
@import '../../blocks/less/related-cases-block.less';
@import '../../blocks/less/companions-block.less';
@import 'workshop-form.less';

@font-face {
  font-family: 'Filson Pro';
  src:  url('assets/fonts/Filson Pro.woff2') format('woff2'),
        url('assets/fonts/Filson Pro.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

::-webkit-scrollbar {
  width: @vw10;
}

::-webkit-scrollbar-track {
  background: @almostWhite;
}

::-webkit-scrollbar-thumb {
  border-radius: @vw50;
  background: rgba(0,0,0,.1);
}

.block__headline {
    padding: 20px 15px 30px;
    background: #fafafa;
    text-align: center;
}
.block__headline-title {
    font-family: 'Arial', sans-serif;
    font-size: 30px;
    font-weight: bold;
    position: relative;
}
.block__headline-title:after {
    content: '';
    display: block;
    width: 40px;
    height: 2px;
    background: #333;
    margin: 0 auto;
}

html.has-scroll-smooth {
	backface-visibility: hidden;
	transform: translateZ(0);
  [data-load-container] {
  	position: fixed;
  	top: 0;
  	right: 0;
  	bottom: 0;
  	left: 0;
  	width: 100vw;
  }
}

// Swup

.transition-fade {
  transition: .75s;
  opacity: 1;
}

html.is-animating .transition-fade {
  opacity: 0;
}

@media all and (max-width: 1160px) {
  ::-webkit-scrollbar {
    width: @vw10-1160;
  }

  ::-webkit-scrollbar-thumb {
    border-radius: @vw50-1160;
  }
}

@media all and (max-width: 580px) {
  ::-webkit-scrollbar {
    width: @vw10-580;
  }

  ::-webkit-scrollbar-thumb {
    border-radius: @vw50-580;
  }
}
