header {
  padding-top: @vw10;
  position: fixed;
  width: 100%;
  top: 0;
  left: 0;
  z-index: 99;
  &.inview {
    .col {
      opacity: 1;
      transform: translateY(0);
      .transition(.3s);
      &:last-child {
        transition-delay: .15s;
      }
    }
  }
  &.menuOpen {
    .col {
      .logo {
        svg {
          path, rect[data-name="Rectangle 3"] {
            fill: @hardWhite;
          }
        }
      }
      .menuToggle {
        .innerContent {
          color: @hardWhite;
        }
        .hamburger {
          &:before, &:after {
            background: @hardWhite;
          }
          &:before {
            transform: rotate(-45deg) translate(-@vw12,-@vw12);
            left: 50%;
          }
          &:after {
            transform: rotate(45deg) translate(-@vw15,@vw9);
            left: 50%;
          }
        }
      }
    }
    .login {
      border-color: @hardWhite;
      color: @hardWhite;
    }
  }
  .col {
    display: inline-block;
    vertical-align: top;
    width: 50%;
    opacity: 0;
    transform: translateY(-@vw20);
    &:nth-child(2){
      text-align: right;
    }
    .languageSelector {
      display: inline-block;
      position: relative;
      text-align: center;
      line-height: @vw40;
      height: @vw40;
      width: @vw40;
      margin-left: @vw20;
      border-radius: 50%;
      border: 1px solid @primaryColor;
      color: @primaryColor;
      text-decoration: none;
      cursor: pointer;
      vertical-align: middle;

      img {
        height: @vw20;
        width: @vw20;
        object-fit: cover;
        margin: auto;
        border-radius: 50%;
      }
      .trp-ls-shortcode-disabled-language {
        height: 100%;
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        align-items: center;
      }

      a {
        text-decoration: none;
        color: @primaryColor;
        padding: 0;
        border-radius: 0;
        transition: background-color 0.3s ease, color 0.3s ease;

        &:hover {
          background-color: transparent;
          color: @hardWhite;
        }
      }
      .trp_language_switcher_shortcode {
        height: 100%;
      }
      .trp-language-switcher {
        top: 0;
        left: 0;
        background: none;
        border: none;
        width: 100%;
        height: 100%;
        &:hover {
          .trp-ls-shortcode-current-language {
            visibility: visible;
          }
        }
        > div {
          background-image: none;
        }
        .trp-language-switcher-container {
          height: 100%;
        }
        .trp-ls-shortcode-current-language {
          top: 0;
          left: 0;
          background: none;
          border: none;
          padding: 0;
          width: 100% !important;
          height: 100% !important;
        }
      }
      .trp-current-language {
        border: 1px solid @primaryColor;
        background-color: @primaryColor;
        display: flex;
        justify-content: center;
        align-items: center;
        img {
          height: @vw20;
          width: @vw20;
        }
      }

      .trp-language-name {
        display: none;
      }

      &:hover .language-dropdown {
        display: block;
      }

      .trp-ls-shortcode-language {
        position: absolute;
        top: calc(100%);
        left: 50%;
        transform: translateX(-50%);
        background-color: @hardWhite;
        border: 1px solid @primaryColor;
        border-radius: 4px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        padding: @vw10;
        z-index: 10;

        a {
          display: block;
          padding: 0;
          color: @primaryColor;
          text-align: center;
          &:hover {
            background-color: transparent;
          }
        }

        img {
          height: @vw20;
          width: @vw20;
        }
      }
    }
    .logo {
      display: inline-block;
      cursor: pointer;
      width: auto;
      height: @vw60 + @vw5;
      &.mobile {
        display: none;
        visibility: hidden;
      }
      * {
        cursor: pointer;
      }
      svg {
        height: 100%;
        width: auto;
        object-fit: contain;
        path, rect[data-name="Rectangle 3"] {
          .transition(.3s);
        }
      }
    }
    &:last-child {
      text-align: right;
    }
    .menuToggle {
      cursor: pointer;
      display: inline-block;
      vertical-align: middle;
      .transitionMore(opacity, .3s, 0s, ease-in-out);
      &:hover {
        opacity: .6;
      }
      * {
        cursor: pointer;
      }
      .innerContent {
        .transition(.3s);
      }
      .hamburger {
        height: @vw5;
        width: @vw20;
        margin-left: @vw14;
        position: relative;
        display: inline-block;
        vertical-align: middle;
        &:after, &:before {
          content: '';
          left: 0;
          position: absolute;
          top: 0;
          width: 100%;
          height: 1px;
          background: @primaryColor;
          .transition(.3s);
        }
        &:after {
          top: auto;
          bottom: 0;
        }
      }
    }
    .button {
      display: inline-block;
      margin-left: @vw20;
      vertical-align: middle;
    }
  }
  .login {
    display: inline-block;
    text-align: center;
    line-height: @vw40;
    height: @vw40;
    width: @vw40;
    margin-left: @vw20;
    border-radius: 50%;
    border: 1px solid @primaryColor;
    color: @primaryColor;
    text-decoration: none;
    cursor: pointer;
    vertical-align: middle;
    .transition(.3s);
    i {
      font-size: @vw16;
      padding-left: @vw1;
    }
    &:hover {
      color: @hardWhite;
      background-color: @primaryColor;
    }
  }

}

#menu {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 97;
  opacity: 0;
  pointer-events: none;
  width: 100vw;
  height: 100vh;
  .transition(.3s);
  transition-delay: .6s;
  &.active {
    pointer-events: all;
    opacity: 1;
    transition: 0s;
    transition-delay: 0s;
    .background {
      opacity: 1;
      border-radius: 0;
      transform: translate(-50%, -50%) scale(1) rotate(0deg);
      transition: transform 0.6s cubic-bezier(0.68, -0.6, 0.32, 1.6), border-radius .3s .3s ease-in-out;
    }
    .cols {
      .col {
        opacity: 1;
        transform: translateY(0);
        &:nth-child(1) {
          transition-delay: .6s;
        }
        &:nth-child(2) {
          transition-delay: .75s;
        }
        &:nth-child(3) {
          transition-delay: .9s;
        }
        &:nth-child(4) {
          transition-delay: 1.2s;
        }
      }
    }
  }
  .background {
    background: @primaryColor;
    position: absolute;
    top: 50%;
    left: 50%;
    opacity: 0;
    height: 100%;
    width: 100%;
    border-radius: @vw100 + @vw100;
    transform: translate(-50%, -50%) scale(.2) rotate(180deg);
    .transition(.3s);
    transition-delay: .9s;
  }
  .normalTitle {
    li {
      margin-bottom: @vw10;
    }
    a {
      opacity: .4;
      .transition(.3s);
      &:hover {
        opacity: 1;
      }
    }
  }
  .contactLink {
    display: table;
    &:first-child {
      margin-bottom: @vw10;
    }
  }
  .cols {
    padding-top: @vw100 + @vw50;
    .col {
      display: inline-block;
      width: 50%;
      vertical-align: top;
      transform: translateY(@vw50);
      opacity: 0;
      .transition(.3s);
      transition-delay: 0s;
      .title {
        ul {
          li {
            margin-bottom: @vw30;
            position: relative;
            a {
              padding-right: @vw80;
              position: relative;
              .transition(.3s);
              &:hover {
                padding-right: 0;
                padding-left: @vw80;
                &:after {
                  left: 0;
                  opacity: 1;
                  transition-delay: .15s;
                  transform: translateY(-50%) translateX(0%);
                }
              }
              &:after {
                content: "\e906";
                font-family: "icomoon";
                position: absolute;
                top: 50%;
                transform: translateY(-50%) translateX(-100%);
                left: 0;
                color: @hardWhite;
                opacity: 0;
                .transition(.3s);
              }
            }
          }
        }
      }
      ul {
        list-style: none;
        &.hover {
          li {
            opacity: .2;
            &.active {
              opacity: 1;
            }
          }
        }
        li {
          .transition(.3s);
          &:last-child {
            margin-bottom: 0;
          }
        }
        a {
          color: @hardWhite;
          text-decoration: none;
        }
      }
    }
  }
}

@media all and (max-width: 1160px) {
  header {
    padding-top: @vw10-1160;
    padding-bottom: @vw10-1160;
    &.menuOpen {
      .col {
        .menuToggle {
          .hamburger {
            &:before {
              transform: rotate(-45deg) translate(-@vw12-1160,-@vw12-1160);
              left: 50%;
            }
            &:after {
              transform: rotate(45deg) translate(-@vw15-1160,@vw9-1160);
              left: 50%;
            }
          }
        }
      }
    }
    .col {
      transform: translateY(-@vw20-1160);
      .languageSelector {
         line-height: @vw40-1160;
         height: @vw40-1160;
         width: @vw40-1160;
         margin-left: @vw20-1160;

         img {
           height: @vw20-1160;
           width: @vw20-1160;
         }

         .trp-current-language img {
           height: @vw20-1160;
           width: @vw20-1160;
         }

         .trp-ls-shortcode-language {
           padding: @vw10-1160;

           img {
             height: @vw20-1160;
             width: @vw20-1160;
           }
         }
       }
      .logo {
        height: @vw60-1160 + @vw5-1160;
      }
      .menuToggle {
        .hamburger {
          height: @vw5-1160;
          width: @vw20-1160;
          margin-left: @vw14-1160;
        }
      }
      .button {
        margin-left: @vw20-1160;
      }
    }
    .login {
      line-height: @vw40-1160;
      height: @vw40-1160;
      width: @vw40-1160;
      margin-left: @vw20-1160;
      i {
        font-size: @vw16-1160;
        padding-left: @vw1-1160;
      }
    }

  }


  #menu {
    .background {
      border-radius: @vw100-1160 + @vw100-1160;
    }
    .cols {
      padding-top: @vw100-1160 + @vw50-1160;
      .col {
        transform: translateY(@vw50-1160);
        .title {
          ul {
            li {
              margin-bottom: @vw30-1160;
              a {
                padding-right: @vw80-1160;
              }
            }
          }
        }
      }
    }
  }
}

@media all and (max-width: 580px) {
  header {
    padding-top: @vw10-580;
    &.menuOpen {
      .col {
        .menuToggle {
          .hamburger {
            &:before {
              transform: rotate(-45deg) translate(-@vw12-580,-@vw13-580);
              left: 50%;
            }
            &:after {
              transform: rotate(45deg) translate(-@vw15-580 ,@vw10-580);
              left: 50%;
            }
          }
        }
      }
    }
    .contentWrapper {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-wrap: wrap;
    }
    .col {
      height: 100%;
      transform: translateY(-@vw20-580);
      &:first-child {
        width: 20%;
      }
      &:last-child {
        width: 80%;
        display: inline-flex;
        flex-wrap: wrap;
        flex-direction: row-reverse;
        align-items: center;
      }
      .languageSelector {
        line-height: @vw50-580;
        height: @vw50-580;
        width: @vw50-580;
        margin-left: @vw20-580;

        img {
          height: @vw20-580;
          width: @vw20-580;
        }

        .trp-current-language img {
          height: @vw20-580;
          width: @vw20-580;
        }

        .trp-ls-shortcode-language {
          padding: @vw10-580;

          img {
            height: @vw20-580;
            width: @vw20-580;
          }
        }
      }
      .logo {
        height: @vw60-580 + @vw5-580;
        &:not(.mobile) {
          display: none;
          visibility: hidden;
        }
        &.mobile {
          display: block;
          visibility: visible;
        }
      }
      .menuToggle {
        margin-left: @vw22-580;
        .hamburger {
          height: @vw7-580;
          width: @vw20-580;
          margin-left: @vw14-580;
          &:before, &:after {
            height: 2px;
          }
        }
      }
      .button {
        display: none;
      }
    }
    .login {
      line-height: @vw50-580;
      height: @vw50-580;
      width: @vw50-580;
      margin-left: @vw20-580;
      i {
        font-size: @vw16-580;
        padding-left: @vw1-580;
      }
    }
  }

  #menu {
    .background {
      border-radius: @vw100-580 + @vw100-580;
    }
    .cols {
      padding-top: @vw100-580 + @vw50-580;
      &:last-child {
        padding-top: @vw50-580;
        .col {
          &:last-child {
            margin-top: @vw32-580;
          }
        }
      }
      .col {
        transform: translateY(@vw50-580);
        width: 100%;
        .title {
          ul {
            li {
              margin-bottom: @vw30-580;
              a {
                padding-right: @vw80-580;
              }
            }
          }
        }
        .contactLink {
          &:not(first-child) {
            margin-top: @vw22-580;
          }
        }
      }
    }
  }
}
