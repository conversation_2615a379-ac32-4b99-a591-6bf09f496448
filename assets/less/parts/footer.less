footer {
  background: @primaryColor;
  border-radius: 0 @vw100 0 @vw100;
  padding: @vw80 0 @vw40 0;
  .logo {
    display: inline-block;
    cursor: pointer;
    width: auto;
    height: @vw60 + @vw5;
    * {
      cursor: pointer;
    }
    svg {
      height: 100%;
      width: auto;
      object-fit: contain;
    }
  }
  .topFooter {
    .contactLinks {
      margin-top: @vw40;
      .contactLink {
        display: table;
        &:first-child {
          margin-bottom: @vw10;
        }
      }
    }
    .normalTitle, .smallMenu {
      a {
        color: @hardWhite;
        text-decoration: none;
        .transition(.3s);
        &:hover {
          opacity: .4;
        }
      }
    }
    .cols {
      .col {
        display: inline-block;
        width: 33.3333%;
        vertical-align: top;
        padding-right: (@vw112 + @vw16 + @vw16);
      }
    }
    .smallMenu {
      margin-top: @vw50;
    }
  }
  .bottomFooter {
    margin-top: @vw80;
    .border {
      color: @secondaryColor;
      padding: @vw16;
      position: relative;
      &:before {
        background: @secondaryColor;
        position: absolute;
        content: '';
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: .1;
      }
      a {
        color: @secondaryColor;
      }
      .col {
        display: inline-block;
        vertical-align: middle;
        width: 50%;
        &:last-child {
          text-align: right;
        }
      }
    }
  }
}

@media all and (max-width: 1160px) {
  footer {
    border-radius: 0 @vw100-1160 0 @vw100-1160;
    padding: @vw80-1160 0 @vw40-1160 0;
    .logo {
      height: @vw60-1160 + @vw5-1160;
    }
    .topFooter {
      .contactLinks {
        margin-top: @vw40-1160;
        .contactLink {
          &:first-child {
            margin-bottom: @vw10-1160;
          }
        }
      }
      .cols {
        .col {
          padding-right: @vw32-1160;
        }
      }
      .smallMenu {
        margin-top: @vw50-1160;
      }
    }
    .bottomFooter {
      margin-top: @vw80-1160;
      .border {
        padding: @vw16-1160;
        &:before {
          background: @secondaryColor;
          opacity: .1;
        }
        .col {
          &:last-child {
            text-align: right;
          }
        }
      }
    }
  }
}

@media all and (max-width: 580px) {
  footer {
    border-radius: 0 @vw100-580 0 0;
    padding: @vw80-580 0 @vw40-580 0;
    .logo {
      height: @vw60-580 + @vw5-580;
      svg {
        width: 100%;
      }
    }
    .topFooter {
      .contactLinks {
        margin-top: @vw40-580;
        .contactLink {
          &:first-child {
            margin-bottom: @vw10-580;
          }
        }
      }
      ul {
        li {
          &:not(:last-child) {
            margin-bottom: @vw10-580;
          }
        }
      }
      .cols {
        .col {
          width: 50%;
          padding-right: @vw22-580;
          &:first-child {
            margin-bottom: @vw32-580;
            width: 100%;
            padding-right: 0;
          }
        }
      }
      .smallMenu {
        margin-top: @vw50-580;
      }
    }
    .bottomFooter {
      margin-top: @vw80-580;
      .border {
        padding: @vw16-580;
        &:before {
          background: @secondaryColor;
          opacity: .1;
        }
        .col {
          &:last-child {
            text-align: right;
          }
        }
      }
    }
  }
}
