@font-face {
  font-family: 'Poppins';
    src: url('assets/fonts/Poppins-Medium.woff2') format('woff2'),
         url('assets/fonts/Poppins-Medium.woff') format('woff'),
         url('assets/fonts/Poppins-Medium.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
  font-display: block;
}

@font-face {
  font-family: 'Poppins';
    src: url('assets/fonts/Poppins-Bold.woff2') format('woff2'),
         url('assets/fonts/Poppins-Bold.woff') format('woff'),
         url('assets/fonts/Poppins-Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
  font-display: block;
}

@font-face {
  font-family: 'icomoon';
  src:  url('assets/fonts/icomoon.eot?3zgica');
  src:  url('assets/fonts/icomoon.eot?3zgica#iefix') format('embedded-opentype'),
    url('assets/fonts/icomoon.ttf?3zgica') format('truetype'),
    url('assets/fonts/icomoon.woff?3zgica') format('woff'),
    url('assets/fonts/icomoon.svg?3zgica#icomoon') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-flower:before {
  content: "\e900";
}
.icon-phone:before {
  content: "\e901";
}
.icon-location:before {
  content: "\e902";
}
.icon-timeline:before {
  content: "\e903";
}
.icon-envelope:before {
  content: "\e904";
}
.icon-copy:before {
  content: "\e905";
}
.icon-arrow-right:before {
  content: "\e906";
}
.icon-arrow-down:before {
  content: "\e907";
}
.icon-arrow-left:before {
  content: "\e908";
}
.icon-arrow-up:before {
  content: "\e909";
}
.icon-share:before {
  content: "\e90a";
}
.icon-linkedin:before {
  content: "\e90b";
}
.icon-check:before {
  content: "\e90c";
}
.icon-file:before {
  content: "\e90d";
}
.icon-globe:before {
  content: "\e90e";
}
.icon-user:before {
  content: "\e90f";
}
