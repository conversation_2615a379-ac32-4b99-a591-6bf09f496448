/**
 * Workshop Form Styling
 * Styling voor workshop registratie formulieren
 */
@lighter Grey;
/* <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> waarde is meer dan 100vw */
.workshop-select.select2-container .select2-selection--single {
  background-color: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 0;
  height: 2.315vw;
  line-height: 2.315vw;
}
.workshop-select.select2-container .select2-selection--single .select2-selection__rendered {
  color: #133535;
  font-family: 'Poppins', arial, sans-serif;
  font-size: 0.926vw;
  font-weight: 400;
  line-height: 2.315vw;
  padding-left: 1.157vw;
  padding-right: 1.157vw;
}
.workshop-select.select2-container .select2-selection--single .select2-selection__placeholder {
  color: rgba(19, 53, 53, 0.7);
}
.workshop-select.select2-container .select2-selection--single .select2-selection__arrow {
  height: 2.315vw;
  right: 1.157vw;
}
.workshop-select.select2-container .select2-selection--single .select2-selection__arrow b {
  border-color: #133535 transparent transparent transparent;
  border-style: solid;
  border-width: 5px 4px 0 4px;
  height: 0;
  left: 50%;
  margin-left: -4px;
  margin-top: -2px;
  position: absolute;
  top: 50%;
  width: 0;
}
.workshop-select.select2-container .select2-selection--single:hover,
.workshop-select.select2-container .select2-selection--single:focus {
  background-color: rgba(255, 255, 255, 0.3);
  -webkit-transition: all 0.3s ease-out;
  -moz-transition: all 0.3s ease-out;
  -o-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
}
.workshop-select.select2-container.select2-container--open .select2-selection--single .select2-selection__arrow b {
  border-color: transparent transparent #133535 transparent;
  border-width: 0 4px 5px 4px;
}
.workshop-select.wpcf7-not-valid.select2-container .select2-selection--single {
  background-color: rgba(255, 0, 0, 0.1);
  border: 1px solid rgba(255, 0, 0, 0.3);
}
.select2-container--default .select2-results > .select2-results__options {
  background-color: white;
  border: 1px solid rgba(19, 53, 53, 0.2);
  border-top: none;
  max-height: 200px;
  overflow-y: auto;
}
.select2-container--default .select2-results > .select2-results__options .select2-results__option {
  color: #133535;
  font-family: 'Poppins', arial, sans-serif;
  font-size: 0.81vw;
  padding: 0.579vw 1.157vw;
  -webkit-transition: all 0.2s ease-out;
  -moz-transition: all 0.2s ease-out;
  -o-transition: all 0.2s ease-out;
  transition: all 0.2s ease-out;
}
.select2-container--default .select2-results > .select2-results__options .select2-results__option[aria-selected="true"] {
  background-color: #133535;
  color: white;
}
.select2-container--default .select2-results > .select2-results__options .select2-results__option.select2-results__option--highlighted {
  background-color: rgba(19, 53, 53, 0.1);
  color: #133535;
}
.wpcf7-form input[type="submit"].loading {
  opacity: 0.7;
  cursor: not-allowed;
  position: relative;
}
.wpcf7-form input[type="submit"].loading:after {
  content: '';
  position: absolute;
  top: 50%;
  right: 0.868vw;
  width: 0.926vw;
  height: 0.926vw;
  margin-top: -0.463vw;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}
.wpcf7-not-valid-tip {
  color: #ff6b6b;
  font-size: 0.694vw;
  margin-top: 0.289vw;
  display: block;
  font-family: 'Poppins', arial, sans-serif;
}
.wpcf7-mail-sent-ok {
  background-color: rgba(76, 175, 80, 0.1);
  border: 1px solid rgba(76, 175, 80, 0.3);
  color: #4caf50;
  padding: 0.868vw 1.157vw;
  border-radius: 4px;
  margin-top: 1.157vw;
  font-family: 'Poppins', arial, sans-serif;
  font-size: 0.81vw;
}
@media all and (max-width: 1160px) {
  .workshop-select.select2-container .select2-selection--single {
    height: 3.448vw;
    line-height: 3.448vw;
  }
  .workshop-select.select2-container .select2-selection--single .select2-selection__rendered {
    font-size: 1.379vw;
    line-height: 3.448vw;
    padding-left: 1.724vw;
    padding-right: 1.724vw;
  }
  .workshop-select.select2-container .select2-selection--single .select2-selection__arrow {
    height: 3.448vw;
    right: 1.724vw;
  }
  .select2-container--default .select2-results > .select2-results__options .select2-results__option {
    font-size: 1.207vw;
    padding: 0.862vw 1.724vw;
  }
  .wpcf7-not-valid-tip {
    font-size: 1.034vw;
    margin-top: 0.431vw;
  }
  .wpcf7-mail-sent-ok {
    padding: 1.293vw 1.724vw;
    margin-top: 1.724vw;
    font-size: 1.207vw;
  }
}
@media all and (max-width: 580px) {
  .workshop-select.select2-container .select2-selection--single {
    height: 6.897vw;
    line-height: 6.897vw;
  }
  .workshop-select.select2-container .select2-selection--single .select2-selection__rendered {
    font-size: 3.621vw;
    line-height: 6.897vw;
    padding-left: 3.448vw;
    padding-right: 3.448vw;
  }
  .workshop-select.select2-container .select2-selection--single .select2-selection__arrow {
    height: 6.897vw;
    right: 3.448vw;
  }
  .select2-container--default .select2-results > .select2-results__options .select2-results__option {
    font-size: 3.621vw;
    padding: 1.724vw 3.448vw;
  }
  .wpcf7-not-valid-tip {
    font-size: 3.621vw;
    margin-top: 0.862vw;
  }
  .wpcf7-mail-sent-ok {
    padding: 2.586vw 3.448vw;
    margin-top: 3.448vw;
    font-size: 3.621vw;
  }
}
.workshop-tooltip {
  background-color: rgba(19, 53, 53, 0.95);
  color: white;
  padding: 0.579vw 0.868vw;
  border-radius: 4px;
  font-size: 0.694vw;
  font-family: 'Poppins', arial, sans-serif;
  margin-top: 0.289vw;
  position: relative;
  -webkit-transition: all 0.3s ease-out;
  -moz-transition: all 0.3s ease-out;
  -o-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
}
.workshop-tooltip:before {
  content: '';
  position: absolute;
  top: -5px;
  left: 1.157vw;
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-bottom: 5px solid rgba(19, 53, 53, 0.95);
}
.workshop-tooltip p {
  margin: 0;
}
.workshop-tooltip p strong {
  font-weight: 600;
}
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
.wpcf7-form input:focus,
.wpcf7-form select:focus,
.wpcf7-form textarea:focus {
  outline: 2px solid rgba(19, 53, 53, 0.5);
  outline-offset: 2px;
}
.form-auto-save-indicator {
  position: fixed;
  top: 1.157vw;
  right: 1.157vw;
  background-color: rgba(76, 175, 80, 0.9);
  color: white;
  padding: 0.463vw 0.694vw;
  border-radius: 4px;
  font-size: 0.694vw;
  font-family: 'Poppins', arial, sans-serif;
  z-index: 9999;
  opacity: 0;
  transform: translateY(-10px);
  -webkit-transition: all 0.3s ease-out;
  -moz-transition: all 0.3s ease-out;
  -o-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
}
.form-auto-save-indicator.show {
  opacity: 1;
  transform: translateY(0);
}
@media all and (max-width: 1160px) {
  .workshop-tooltip {
    padding: 0.862vw 1.293vw;
    font-size: 1.034vw;
    margin-top: 0.431vw;
  }
  .workshop-tooltip:before {
    left: 1.724vw;
  }
  .form-auto-save-indicator {
    top: 1.724vw;
    right: 1.724vw;
    padding: 0.69vw 1.034vw;
    font-size: 1.034vw;
  }
}
@media all and (max-width: 580px) {
  .workshop-tooltip {
    padding: 1.724vw 2.586vw;
    font-size: 3.621vw;
    margin-top: 0.862vw;
  }
  .workshop-tooltip:before {
    left: 3.448vw;
  }
  .form-auto-save-indicator {
    top: 3.448vw;
    right: 3.448vw;
    padding: 1.379vw 2.069vw;
    font-size: 3.621vw;
  }
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
