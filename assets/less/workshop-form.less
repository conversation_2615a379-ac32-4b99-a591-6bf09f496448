/**
 * Workshop Form Styling
 * Styling voor workshop registratie formulieren
 */

@import 'constants.less';

// Workshop select styling
.workshop-select {
  &.select2-container {
    .select2-selection--single {
      background-color: rgba(255,255,255,.2);
      border: none;
      border-radius: 0;
      height: @vw40;
      line-height: @vw40;
      
      .select2-selection__rendered {
        color: @primaryColor;
        font-family: 'Poppins', arial, sans-serif;
        font-size: @vw16;
        font-weight: 400;
        line-height: @vw40;
        padding-left: @vw20;
        padding-right: @vw20;
      }
      
      .select2-selection__placeholder {
        color: rgba(@primaryColor, 0.7);
      }
      
      .select2-selection__arrow {
        height: @vw40;
        right: @vw20;
        
        b {
          border-color: @primaryColor transparent transparent transparent;
          border-style: solid;
          border-width: 5px 4px 0 4px;
          height: 0;
          left: 50%;
          margin-left: -4px;
          margin-top: -2px;
          position: absolute;
          top: 50%;
          width: 0;
        }
      }
      
      &:hover, &:focus {
        background-color: rgba(255,255,255,.3);
        .transition(.3s);
      }
    }
    
    &.select2-container--open {
      .select2-selection--single {
        .select2-selection__arrow b {
          border-color: transparent transparent @primaryColor transparent;
          border-width: 0 4px 5px 4px;
        }
      }
    }
  }
  
  // Error state
  &.wpcf7-not-valid {
    &.select2-container .select2-selection--single {
      background-color: rgba(255, 0, 0, 0.1);
      border: 1px solid rgba(255, 0, 0, 0.3);
    }
  }
}

// Workshop dropdown styling
.select2-container--default .select2-results > .select2-results__options {
  background-color: white;
  border: 1px solid rgba(@primaryColor, 0.2);
  border-top: none;
  max-height: 200px;
  overflow-y: auto;
  
  .select2-results__option {
    color: @primaryColor;
    font-family: 'Poppins', arial, sans-serif;
    font-size: @vw14;
    padding: @vw10 @vw20;
    .transition(.2s);
    
    &[aria-selected="true"] {
      background-color: @primaryColor;
      color: white;
    }
    
    &.select2-results__option--highlighted {
      background-color: rgba(@primaryColor, 0.1);
      color: @primaryColor;
    }
  }
}

// Loading state voor submit button
.wpcf7-form {
  input[type="submit"] {
    &.loading {
      opacity: 0.7;
      cursor: not-allowed;
      position: relative;
      
      &:after {
        content: '';
        position: absolute;
        top: 50%;
        right: @vw15;
        width: @vw16;
        height: @vw16;
        margin-top: -@vw8;
        border: 2px solid transparent;
        border-top: 2px solid currentColor;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
    }
  }
}

// Workshop error styling
.wpcf7-not-valid-tip {
  color: #ff6b6b;
  font-size: @vw12;
  margin-top: @vw5;
  display: block;
  font-family: 'Poppins', arial, sans-serif;
}

// Success message enhancement
.wpcf7-mail-sent-ok {
  background-color: rgba(76, 175, 80, 0.1);
  border: 1px solid rgba(76, 175, 80, 0.3);
  color: #4caf50;
  padding: @vw15 @vw20;
  border-radius: 4px;
  margin-top: @vw20;
  font-family: 'Poppins', arial, sans-serif;
  font-size: @vw14;
}

// Responsive styling
@media all and (max-width: 1160px) {
  .workshop-select {
    &.select2-container {
      .select2-selection--single {
        height: @vw40-1160;
        line-height: @vw40-1160;
        
        .select2-selection__rendered {
          font-size: @vw16-1160;
          line-height: @vw40-1160;
          padding-left: @vw20-1160;
          padding-right: @vw20-1160;
        }
        
        .select2-selection__arrow {
          height: @vw40-1160;
          right: @vw20-1160;
        }
      }
    }
  }
  
  .select2-container--default .select2-results > .select2-results__options {
    .select2-results__option {
      font-size: @vw14-1160;
      padding: @vw10-1160 @vw20-1160;
    }
  }
  
  .wpcf7-not-valid-tip {
    font-size: @vw12-1160;
    margin-top: @vw5-1160;
  }
  
  .wpcf7-mail-sent-ok {
    padding: @vw15-1160 @vw20-1160;
    margin-top: @vw20-1160;
    font-size: @vw14-1160;
  }
}

@media all and (max-width: 580px) {
  .workshop-select {
    &.select2-container {
      .select2-selection--single {
        height: @vw40-580;
        line-height: @vw40-580;
        
        .select2-selection__rendered {
          font-size: @vw21-580;
          line-height: @vw40-580;
          padding-left: @vw20-580;
          padding-right: @vw20-580;
        }
        
        .select2-selection__arrow {
          height: @vw40-580;
          right: @vw20-580;
        }
      }
    }
  }
  
  .select2-container--default .select2-results > .select2-results__options {
    .select2-results__option {
      font-size: @vw21-580;
      padding: @vw10-580 @vw20-580;
    }
  }
  
  .wpcf7-not-valid-tip {
    font-size: @vw21-580;
    margin-top: @vw5-580;
  }
  
  .wpcf7-mail-sent-ok {
    padding: @vw15-580 @vw20-580;
    margin-top: @vw20-580;
    font-size: @vw21-580;
  }
}

// Workshop tooltip styling
.workshop-tooltip {
  background-color: rgba(@primaryColor, 0.95);
  color: white;
  padding: @vw10 @vw15;
  border-radius: 4px;
  font-size: @vw12;
  font-family: 'Poppins', arial, sans-serif;
  margin-top: @vw5;
  position: relative;
  .transition(.3s);

  &:before {
    content: '';
    position: absolute;
    top: -5px;
    left: @vw20;
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-bottom: 5px solid rgba(@primaryColor, 0.95);
  }

  p {
    margin: 0;

    strong {
      font-weight: 600;
    }
  }
}

// Screen reader only content
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

// Form field focus improvements
.wpcf7-form {
  input, select, textarea {
    &:focus {
      outline: 2px solid rgba(@primaryColor, 0.5);
      outline-offset: 2px;
    }
  }
}

// Auto-save indicator
.form-auto-save-indicator {
  position: fixed;
  top: @vw20;
  right: @vw20;
  background-color: rgba(76, 175, 80, 0.9);
  color: white;
  padding: @vw8 @vw12;
  border-radius: 4px;
  font-size: @vw12;
  font-family: 'Poppins', arial, sans-serif;
  z-index: 9999;
  opacity: 0;
  transform: translateY(-10px);
  .transition(.3s);

  &.show {
    opacity: 1;
    transform: translateY(0);
  }
}

// Responsive styling voor nieuwe elementen
@media all and (max-width: 1160px) {
  .workshop-tooltip {
    padding: @vw10-1160 @vw15-1160;
    font-size: @vw12-1160;
    margin-top: @vw5-1160;

    &:before {
      left: @vw20-1160;
    }
  }

  .form-auto-save-indicator {
    top: @vw20-1160;
    right: @vw20-1160;
    padding: @vw8-1160 @vw12-1160;
    font-size: @vw12-1160;
  }
}

@media all and (max-width: 580px) {
  .workshop-tooltip {
    padding: @vw10-580 @vw15-580;
    font-size: @vw21-580;
    margin-top: @vw5-580;

    &:before {
      left: @vw20-580;
    }
  }

  .form-auto-save-indicator {
    top: @vw20-580;
    right: @vw20-580;
    padding: @vw8-580 @vw12-580;
    font-size: @vw21-580;
  }
}

// Spin animation voor loading state
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
