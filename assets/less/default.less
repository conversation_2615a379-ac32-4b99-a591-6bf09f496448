* {
  box-sizing: border-box;
  cursor:default;
  letter-spacing: 0;
  margin:0;
  padding:0;
  position:relative;
  &::selection {
    background: @primaryColor;
    color: @hardWhite;
  }
  &::-webkit-selection {
    background: @primaryColor;
    color: @hardWhite;
  }
}

html.lenis {
  height: auto;
}

.lenis.lenis-smooth {
  scroll-behavior: auto;
}

.lenis.lenis-smooth [data-lenis-prevent] {
  overscroll-behavior: contain;
}

.lenis.lenis-stopped {
  overflow: hidden;
}

.lenis.lenis-scrolling iframe {
  pointer-events: none;
}
html {
  overflow-x: hidden;
}
body {
  background: @hardWhite;
  color: @primaryColor;
  font-family: 'Poppins', arial, sans-serif;
  overflow-x: hidden;
  font-size: @vw16;
  line-height: @vw22;
  strong {
    font-style: italic;
    font-weight: 600;
  }
  a {
    cursor: pointer;
    * {
      cursor: pointer;
    }
  }
  p {
    font-family: 'Poppins', arial, sans-serif;
    font-size: @vw16;
    color: @primaryColor;
    font-weight: 400;
    line-height: @vw22;
    &:first-child {
      font-weight: 600;
    }
    a {
      color: @secondaryColor;
      cursor: pointer;
      text-decoration: none;
      font-weight: 700;
      .transition(.15s);
      &:hover {
        opacity: .6;
      }
    }
  }
}

[data-scroll-section] {
  background: @hardWhite;
  // padding: @vw100 + @vw30 + @vw5 0 0 0;
}

[data-scroll-container] {
  position: absolute;
  top: 0;
  width: 100%;
}

section {
  margin: @vw100 + @vw20 0;
  &:first-child {
    margin-top: 0;
    padding-top: @vw100;
  }
  &.secondary {
    padding: @vw100 0;
    position: relative;
    &:before {
      content: '';
      background: @secondaryColor;
      top: 0;
      left: @vw50;
      position: absolute;
      opacity: .2;
      height: 100%;
      width: calc(100% ~"-" @vw100);
    }
    .subTitle, .bigTitle, .title {
      &.secondary {
        color: @primaryColor;
      }
      &.primary {
        color: @primaryColor;
      }
    }
  }
  &.primary {
    padding: @vw100 0;
    position: relative;
    color: @hardWhite;
    .bigTitle, .title, .normalTitle, p {
      color: @hardWhite;
    }
    &:before {
      content: '';
      background: @primaryColor;
      top: 0;
      left: @vw50;
      position: absolute;
      height: 100%;
      width: calc(100% ~"-" @vw100);
    }
    &.borderRadius {
      &:before {
        border-radius: @vw100 0 @vw100 0;
      }
    }
  }
  &.grey {
    padding: @vw100 0;
    position: relative;
    &:before {
      content: '';
      background: @backgroundGrey;
      top: 0;
      left: @vw50;
      position: absolute;
      height: 100%;
      width: calc(100% ~"-" @vw100);
    }
  }
}

.contentWrapper {
  display: block;
  width: 100vw;
  padding: 0 @vw104;
  margin: auto;
  &.small {
    padding: 0 (@vw99 * 2) + (@vw22 * 3);
  }
  &.smaller {
    padding: 0 (@vw99 * 3) + (@vw22 * 4);
  }
}

.text {
  &.white {
    p {
      color: @hardWhite;
    }
  }
  p {
    margin-bottom: @vw22;
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.transitionLoopSplitter (@i, @transition) when (@i > 0) {
    &:nth-child(@{i}) {
        .innerWrapper {
          transition-delay: (@i * @transition);
        }
    }
    .transitionLoopSplitter(@i - 1, @transition);
}

.bigTitle, .title, .normalTitle {
  &.splitThis {
    opacity: 0;
    &.inview {
      opacity: 1;
      &.animate {
        .wrapper {
          .transitionLoopSplitter(12, .15s);
          .innerWrapper {
            opacity: 1;
            transition: opacity .15s ease-in, transform 0.6s cubic-bezier(0.16, 1, 0.3, 1);
            transform: translateY(0) translateZ(0);
          }
        }
      }
    }
    .wrapper {
      position: relative;
      overflow: hidden;
      .innerWrapper {
        position: absolute;
        top: 0;
        opacity: 0;
        transform: translateY(100%) translateZ(0);
      }
    }
  }
}

.bigTitle {
  font-family: "Poppins";
  font-weight: 500;
  font-size: @vw100;
  line-height: @vw100;
  color: @secondaryColor;
  &.primary {
    color: @primaryColor;
  }
  .primary {
    color: @primaryColor;
  }
  &.splitThis {
    .wrapper {
      height: @vw100 + @vw12;
    }
  }
}

.title {
  font-family: "Poppins";
  font-weight: 500;
  font-size: @vw70;
  line-height: @vw70;
  color: @secondaryColor;
  &.splitThis {
    .wrapper {
      height: @vw70 + @vw10;
    }
  }
  &.primary {
    color: @primaryColor;
  }
  .primary {
    color: @primaryColor;
  }
}

.subTitle {
  font-family: "Poppins";
  font-weight: 400;
  font-size: @vw16;
  line-height: @vw22;
  &.secondary {
    color: @secondaryColor;
    font-weight: 600;
  }
}

.normalTitle {
  font-family: "Poppins";
  font-weight: 500;
  font-size: @vw36;
  line-height: @vw44;
  &.splitThis {
    .wrapper {
      height: @vw44;
    }
  }
  .secondary {
    color: @secondaryColor;
  }
}

.textTitle {
  font-family: "Poppins";
  font-weight: 500;
  font-size: @vw16;
  line-height: @vw22;
  &.secondary {
    color: @secondaryColor;
  }
}

.button {
  background: @secondaryColor;
  border: 2px solid @secondaryColor;
  border-radius: 0 @vw50 0 @vw50;
  color: @primaryColor;
  cursor: pointer;
  text-decoration: none;
  display: inline-block;
  font-size: @vw16;
  position: relative;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: @vw60;
  height: @vw60;
  padding: 0 @vw40;
  .transition(.3s);
  * {
    cursor: pointer;
  }
  &.download {
    display: block;
    span , i {
      display: inline-block;
      vertical-align: middle;
    }
    i {
      width: @vw10;
    }
    .innerText {
      width: calc(100% ~"-" @vw100 );
      position: relative;
      overflow: hidden;
      text-overflow: ellipsis;
      text-align: center;
      height: @vw60;
      overflow: hidden;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      padding: 0 @vw10;
    }
    .type {
      width: @vw50;
    }
  }
  &:hover {
    color: @secondaryColor;
    background: transparent;
    border-radius: @vw50 0 @vw50 0;
  }
  &.outline {
    background: transparent;
    border-color: @hardWhite;
    color: @hardWhite;
    &.primary {
      border-color: @primaryColor;
      color: @primaryColor;
      &:hover {
        background: @primaryColor;
        color: @hardWhite;
      }
    }
    &:hover {
      background: @hardWhite;
      color: @primaryColor;
    }
  }
}

.arrowButton {
  border-radius: 50%;
  cursor: pointer;
  width: @vw50;
  height: @vw50;
  text-align: center;
  line-height: @vw50;
  background: @secondaryColor;
  color: @hardWhite;
  .transition(.3s);
  &.disabled {
    opacity: .4;
    pointer-events: none;
  }
  &:hover {
    background: @primaryColor;
  }
  i {
    cursor: pointer;
  }
}

.textLink {
    color: @thirdColor;
    display: block;
    position: relative;
    text-decoration: none;
    padding-right: @vw30;
    cursor: pointer;
    width: 100%;
    .transition(.3s);
    &:hover {
      color: @primaryColor;
      padding-left: @vw30;
      padding-right: 0;
      &:before {
        width: 100%;
        background: @primaryColor;
        transition-delay: .15s;
      }
      &:after {
        width: 0;
        background: @primaryColor;
      }
      i {
        color: @primaryColor;
        &:first-child {
          left: 0;
          opacity: 1;
          transition-delay: .15s;
        }
        &:last-child {
          right: -@vw10;
          opacity: 0;
          transition-delay: 0s;
        }
      }
    }
    &:before, &:after {
      content: '';
      width: 100%;
      position: absolute;
      top: auto;
      right: 0;
      .transition(.15s);
      bottom: 0;
      opacity: .4;
      height: 1px;
      background: @thirdColor;
    }
    &:before {
      left: 0;
      right: auto;
      width: 0%;
    }
    i {
      color: @thirdColor;
      cursor: pointer;
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      .transition(.3s);
      &:first-child {
        left: -@vw20;
        opacity: 0;
      }
      &:last-child {
        right: 0;
        transition-delay: .15s;
      }
    }
}

.contactLink {
  color: @secondaryColor;
  cursor: pointer;
  * {
    cursor: pointer;
  }
  display: inline-block;
  .transition(.3s);
  &:hover {
    color: @hardWhite;
    .icon {
      border-radius: @vw10 0 @vw10 0;
      background: @hardWhite;
    }
  }
  span {
    display: inline-block;
    vertical-align: middle;
  }
  .icon {
    height: @vw20;
    width: @vw20;
    line-height: @vw20;
    margin-right: @vw10;
    background: @secondaryColor;
    color: @primaryColor;
    .transition(.3s);
    border-radius: 0 @vw10 0 @vw10;
    text-align: center;
    i {
      font-size: @vw12;
    }
  }
}

// select2
.select2-container {
  cursor: pointer;
  width: auto !important;
  display: inline-block;
  &.select2-container--open {
    .select2-selection--single {
      .select2-selection__arrow {
        &:after {
          transform: translateY(-50%) rotate(180deg);
        }
      }
    }
  }
  * {
    cursor: pointer;
  }
  .select2-selection--single {
    background-color: transparent !important;
    border: 2px solid @primaryColor !important;
    height: @vw60 !important;
    border-radius: 0 @vw50 0 @vw50 !important;
    padding: 0 @vw80 !important;
     .select2-selection__rendered {
       line-height: @vw60 - @vw2 !important;
     }
     .select2-selection__arrow {
       top: 50% !important;
       transform: translateY(-50%) !important;
       right: @vw40 !important;
       b {
         display: none !important;
       }
       &:after {
         content: "\e907";
         position: absolute;
         top: 50%;
         transform: translateY(-50%);
         left: 0;
         font-family: "icomoon";
         .transition(.15s);
       }
     }
  }
  .select2-dropdown {
    border: none !important;
    border-radius: 0 !important;
    box-shadow: 0 0 @vw10 rgba(0,0,0,.2);
  }
  .select2-results__option {
    padding: @vw10;
    text-align: center;
    &.select2-results__option--highlighted {
      background-color: @thirdColor !important;
    }
    &.select2-results__option--selected {
      background-color: @hardWhite !important;
      &.select2-results__option--highlighted {
        background-color: @thirdColor !important;
      }
    }
  }
}

@media all and (max-width: 1160px) {

  body {
    font-size: @vw16-1160;
    line-height: @vw22-1160;
    p {
      font-size: @vw16-1160;
      line-height: @vw22-1160;
    }
  }

  section {
    margin: @vw100-1160 + @vw20-1160 0;
    &:first-child {
      padding-top: @vw100-1160;
    }
    &.secondary {
      padding: @vw100-1160 0;
      &:before {
        left: @vw50-1160;
        width: calc(100% - @vw100-1160);
      }
    }
    &.primary {
      padding: @vw100-1160 0;
      &:before {
        left: @vw50-1160;
        width: calc(100% - @vw100-1160);
      }
      &.borderRadius {
        &:before {
          border-radius: @vw100-1160 0 @vw100-1160 0;
        }
      }
    }
    &.grey {
      padding: @vw100-1160 0;
      &:before {
        left: @vw50-1160;
        width: calc(100% - @vw100-1160);
      }
    }
  }

  .contentWrapper {
    padding: 0 @vw99-1160;
    &.smaller {
      padding: 0 @vw99-1160;
    }
  }

  .bigTitle {
    font-size: @vw50-1160;
    line-height: @vw50-1160;
    &.splitThis {
      .wrapper {
        height: @vw55-1160;
      }
    }
  }

  .title {
    font-size: @vw50-1160;
    line-height: @vw50-1160;
    &.splitThis {
      .wrapper {
        height: @vw55-1160;
      }
    }
  }

  .subTitle {
    font-size: @vw16-1160;
    line-height: @vw22-1160;
  }

  .normalTitle {
    font-size: @vw26-1160;
    line-height: @vw32-1160;
    &.splitThis {
      .wrapper {
        height: @vw32-1160;
      }
    }
  }

  .textTitle {
    font-size: @vw16-1160;
    line-height: @vw22-1160;
  }

  .text {
    p {
      margin-bottom: @vw16-1160;
    }
  }

  .button {
    border-radius: 0 @vw50-1160 0 @vw50-1160;
    font-size: @vw16-1160;
    line-height: @vw60-1160;
    height: @vw60-1160;
    padding: 0 @vw30-1160;
    &.download {
      i {
        width: @vw10-1160;
      }
      .innerText {
        width: calc(100% ~"-" @vw100-1160 );
        height: @vw60-1160;
        padding: 0 @vw10-1160;
      }
      .type {
        width: @vw50-1160;
      }
    }
    .innerText {
      height: @vw60-1160;
      padding: 0 @vw10-1160;
      .type {
        width: @vw50-1160;
      }
    }
  }

  .arrowButton {
    width: @vw50-1160;
    height: @vw50-1160;
    line-height: @vw50-1160;
  }

  .textLink {
    padding-right: @vw30-1160;
    &:hover {
      padding-left: @vw30-1160;
    }
    i {
      &:first-child {
        left: -@vw20-1160;
      }
      &:last-child {
        right: 0;
      }
    }
    &:before {
      width: 0%;
    }
  }

  .contactLink {
    &:hover {
      .icon {
        border-radius: @vw10-1160 0 @vw10-1160 0;
      }
    }
    .icon {
      height: @vw20-1160;
      width: @vw20-1160;
      line-height: @vw20-1160;
      margin-right: @vw10-1160;
      border-radius: 0 @vw10-1160 0 @vw10-1160;
      i {
        font-size: @vw12-1160;
      }
    }
  }

  .select2-container {
    .select2-selection--single {
      height: @vw60-1160 !important;
      border-radius: 0 @vw50-1160 0 @vw50-1160 !important;
      padding: 0 @vw80-1160 !important;
      .select2-selection__rendered {
        line-height: @vw60-1160 - @vw2-1160 !important;
      }
      .select2-selection__arrow {
        right: @vw40-1160 !important;
      }
    }
  }
}

@media all and (max-width: 580px) {

  body {
    font-size: @vw21-580;
    line-height: @vw32-580;
    p {
      font-size: @vw21-580;
      line-height: @vw32-580;
    }
  }

  section {

    margin: @vw100-580 + @vw20-580 0;
    &:first-child {
      padding-top: @vw100-580;
    }
    &.secondary {
      padding: @vw100-580 0;
      &:before {
        left: @vw10-580;
        width: calc(100% ~"-" @vw20-580);
      }
    }
    &.primary {
      padding: @vw100-580 0;
      &:before {
        left: @vw10-580;
        width: calc(100% ~"-" @vw20-580);
      }
      &.borderRadius {
        &:before {
          border-radius: @vw100-580 0 @vw100-580 0;
        }
      }
    }
    &.grey {
      padding: @vw100-580 0;
      &:before {
        left: @vw10-580;
        width: calc(100% ~"-" @vw20-580);
      }
    }
  }

  .contentWrapper {
    padding: 0 @vw32-580;
    &.smaller {
      padding: 0 @vw22-580;
    }
  }

  .bigTitle {
    font-size: @vw50-580;
    line-height: @vw50-580;
    &.splitThis {
      .wrapper {
        height: @vw50-580 + @vw10-580;
      }
    }
  }

  .title {
    font-size: @vw32-580;
    line-height: @vw40-580;
    &.splitThis {
      .wrapper {
        height: @vw40-580;
      }
    }
  }

  .subTitle {
    font-size: @vw21-580;
    line-height: @vw32-580;
  }

  .normalTitle {
    font-size: @vw26-580;
    line-height: @vw32-580;
    &.splitThis {
      .wrapper {
        height: @vw32-580;
      }
    }
  }

  .textTitle {
    font-size: @vw21-580;
    line-height: @vw32-580;
  }

  .button {
    border-radius: 0 @vw50-580 0 @vw50-580;
    font-size: @vw21-580;
    line-height: @vw60-580;
    height: @vw60-580 + @vw5-580;
    padding: 0 @vw30-580;
    &.download {
      i {
        width: @vw10-580;
      }
      .innerText {
        width: calc(100% ~"-" @vw100-580 );
        height: @vw60-580;
        padding: 0 @vw10-580;
      }
      .type {
        width: @vw50-580;
      }
    }
    .innerText {
      height: @vw60-580;
      padding: 0 @vw10-580;
      .type {
        width: @vw50-580;
      }
    }
  }

  .arrowButton {
    width: @vw50-580;
    height: @vw50-580;
    line-height: @vw50-580;
  }

  .text {
    p {
      margin-bottom: @vw21-580;
    }
  }

  .textLink {
    padding-right: @vw30-580;
    &:hover {
      padding-left: @vw30-580;
    }
    i {
      &:first-child {
        left: -@vw20-580;
      }
      &:last-child {
        right: 0;
      }
    }
    &:before {
      width: 0%;
    }
  }

  .contactLink {
    &:hover {
      .icon {
        border-radius: @vw10-580 0 @vw10-580 0;
      }
    }
    .icon {
      height: @vw20-580;
      width: @vw20-580;
      line-height: @vw20-580;
      margin-right: @vw10-580;
      border-radius: 0 @vw10-580 0 @vw10-580;
      i {
        font-size: @vw12-580;
      }
    }
  }

  .select2-container {
    .select2-selection--single {
      height: @vw60-580 !important;
      border-radius: 0 @vw50-580 0 @vw50-580 !important;
      padding: 0 @vw80-580 !important;
      .select2-selection__rendered {
        line-height: @vw60-580 - @vw2-580 !important;
      }
      .select2-selection__arrow {
        right: @vw40-580 !important;
      }
    }
  }
}
