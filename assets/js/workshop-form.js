/**
 * Workshop Form Enhancement
 * Verbetert de workshop registratie functionaliteit
 */

$(document).ready(function() {
    initWorkshopForm();
});

// Initialiseer workshop form functionaliteit
function initWorkshopForm() {
    // Verbeter workshop select styling
    enhanceWorkshopSelect();
    
    // Voeg form validatie toe
    addWorkshopFormValidation();
    
    // Verbeter gebruikerservaring
    improveUserExperience();
}

// Verbeter workshop select met Select2
function enhanceWorkshopSelect() {
    // Zoek naar workshop select velden
    $('select[name="workshop"], .wpcf7-form select[name*="workshop"]').each(function() {
        var $select = $(this);
        
        // Initialiseer Select2 als het nog niet is geïnitialiseerd
        if (!$select.hasClass('select2-hidden-accessible')) {
            $select.select2({
                placeholder: 'Selecteer een workshop...',
                allowClear: false,
                minimumResultsForSearch: -1, // Verberg zoekfunctie
                width: '100%'
            });
        }
        
        // Voeg custom styling toe
        $select.addClass('workshop-select');
    });
}

// Voeg form validatie toe
function addWorkshopFormValidation() {
    // Luister naar form submit events
    $(document).on('submit', '.wpcf7-form', function(e) {
        var $form = $(this);
        var $workshopSelect = $form.find('select[name="workshop"], select[name*="workshop"]');
        
        if ($workshopSelect.length > 0) {
            var workshopValue = $workshopSelect.val();
            
            // Controleer of een workshop is geselecteerd
            if (!workshopValue || workshopValue === '') {
                e.preventDefault();
                
                // Voeg error styling toe
                $workshopSelect.addClass('wpcf7-not-valid');
                
                // Toon error message
                showWorkshopError($workshopSelect, 'Selecteer een workshop om door te gaan.');
                
                // Scroll naar het veld
                $('html, body').animate({
                    scrollTop: $workshopSelect.offset().top - 100
                }, 500);
                
                return false;
            }
        }
    });
    
    // Verwijder error styling bij selectie
    $(document).on('change', 'select[name="workshop"], select[name*="workshop"]', function() {
        var $select = $(this);
        $select.removeClass('wpcf7-not-valid');
        $select.siblings('.wpcf7-not-valid-tip').remove();
    });
}

// Toon workshop error message
function showWorkshopError($field, message) {
    // Verwijder bestaande error messages
    $field.siblings('.wpcf7-not-valid-tip').remove();
    
    // Voeg nieuwe error message toe
    $field.after('<span class="wpcf7-not-valid-tip">' + message + '</span>');
}

// Verbeter gebruikerservaring
function improveUserExperience() {
    // Voeg loading state toe tijdens form submit
    $(document).on('submit', '.wpcf7-form', function() {
        var $form = $(this);
        var $submitBtn = $form.find('input[type="submit"]');
        
        // Voeg loading class toe
        $submitBtn.addClass('loading');
        $submitBtn.val('Versturen...');
        
        // Disable submit button om dubbele submits te voorkomen
        $submitBtn.prop('disabled', true);
    });
    
    // Reset form state na response
    $(document).on('wpcf7mailsent wpcf7mailfailed wpcf7spam wpcf7invalid', function(event) {
        var $form = $(event.target);
        var $submitBtn = $form.find('input[type="submit"]');
        
        // Reset submit button
        $submitBtn.removeClass('loading');
        $submitBtn.val('Versturen');
        $submitBtn.prop('disabled', false);
    });
    
    // Verbeter success message
    $(document).on('wpcf7mailsent', function(event) {
        var $form = $(event.target);
        var $workshopSelect = $form.find('select[name="workshop"], select[name*="workshop"]');
        
        if ($workshopSelect.length > 0) {
            var selectedWorkshop = $workshopSelect.find('option:selected').text();
            
            // Voeg workshop naam toe aan success message indien mogelijk
            var $responseOutput = $form.find('.wpcf7-response-output');
            if ($responseOutput.length > 0 && selectedWorkshop) {
                var currentMessage = $responseOutput.html();
                if (currentMessage.indexOf('Workshop:') === -1) {
                    var enhancedMessage = currentMessage.replace(
                        'Workshop:', 
                        'Workshop: ' + selectedWorkshop
                    );
                    $responseOutput.html(enhancedMessage);
                }
            }
        }
    });
}

// Herinitialiseer na Swup page transitions
if (typeof pageContainerWrap !== 'undefined') {
    pageContainerWrap.on('pageView', function() {
        setTimeout(function() {
            initWorkshopForm();
        }, 100);
    });
}

// Herinitialiseer na CF7 ready event
$(document).on('wpcf7:ready', function() {
    setTimeout(function() {
        initWorkshopForm();
    }, 100);
});

// Voeg workshop informatie tooltip toe
function addWorkshopTooltips() {
    $('select[name="workshop"], select[name*="workshop"]').each(function() {
        var $select = $(this);

        // Voeg tooltip container toe
        if (!$select.siblings('.workshop-tooltip').length) {
            $select.after('<div class="workshop-tooltip" style="display: none;"></div>');
        }

        // Luister naar selectie wijzigingen
        $select.on('change', function() {
            var selectedValue = $(this).val();
            var $tooltip = $(this).siblings('.workshop-tooltip');

            if (selectedValue && selectedValue !== '') {
                // Haal workshop details op (dit zou via AJAX kunnen worden uitgebreid)
                showWorkshopDetails($tooltip, selectedValue);
            } else {
                $tooltip.hide();
            }
        });
    });
}

// Toon workshop details
function showWorkshopDetails($tooltip, workshopName) {
    // Voor nu tonen we een eenvoudige bevestiging
    // Dit kan later worden uitgebreid met AJAX calls voor meer details
    $tooltip.html('<p><strong>Geselecteerd:</strong> ' + workshopName + '</p>');
    $tooltip.show();

    // Voeg een subtiele animatie toe
    $tooltip.css({
        opacity: 0,
        transform: 'translateY(-10px)'
    }).animate({
        opacity: 1
    }, 300).css('transform', 'translateY(0)');
}

// Verbeter form accessibility
function improveFormAccessibility() {
    $('select[name="workshop"], select[name*="workshop"]').each(function() {
        var $select = $(this);

        // Voeg ARIA labels toe
        $select.attr('aria-label', 'Selecteer een workshop');
        $select.attr('aria-describedby', 'workshop-help');

        // Voeg help text toe als deze nog niet bestaat
        if (!$select.siblings('#workshop-help').length) {
            $select.after('<div id="workshop-help" class="sr-only">Kies een workshop uit de lijst om je aan te melden</div>');
        }

        // Verbeter keyboard navigatie
        $select.on('keydown', function(e) {
            // Enter key om dropdown te openen
            if (e.keyCode === 13 && !$(this).hasClass('select2-hidden-accessible')) {
                e.preventDefault();
                $(this).trigger('click');
            }
        });
    });
}

// Voeg form analytics toe (optioneel)
function addFormAnalytics() {
    // Track workshop selecties
    $(document).on('change', 'select[name="workshop"], select[name*="workshop"]', function() {
        var selectedWorkshop = $(this).val();

        // Google Analytics event (als beschikbaar)
        if (typeof gtag !== 'undefined' && selectedWorkshop) {
            gtag('event', 'workshop_selected', {
                'event_category': 'form',
                'event_label': selectedWorkshop,
                'value': 1
            });
        }
    });

    // Track form submissions
    $(document).on('wpcf7mailsent', function(event) {
        var $form = $(event.target);
        var $workshopSelect = $form.find('select[name="workshop"], select[name*="workshop"]');

        if ($workshopSelect.length > 0) {
            var selectedWorkshop = $workshopSelect.val();

            // Google Analytics conversion event
            if (typeof gtag !== 'undefined' && selectedWorkshop) {
                gtag('event', 'workshop_registration', {
                    'event_category': 'conversion',
                    'event_label': selectedWorkshop,
                    'value': 1
                });
            }
        }
    });
}

// Voeg auto-save functionaliteit toe
function addAutoSave() {
    var autoSaveKey = 'workshop_form_data';

    // Laad opgeslagen data
    function loadSavedData() {
        var savedData = localStorage.getItem(autoSaveKey);
        if (savedData) {
            try {
                var data = JSON.parse(savedData);

                // Herstel form velden
                Object.keys(data).forEach(function(fieldName) {
                    var $field = $('[name="' + fieldName + '"]');
                    if ($field.length && data[fieldName]) {
                        $field.val(data[fieldName]);

                        // Trigger change event voor select2
                        if ($field.hasClass('select2-hidden-accessible')) {
                            $field.trigger('change');
                        }
                    }
                });

                console.log('Workshop form data hersteld');
            } catch (e) {
                console.log('Fout bij het herstellen van form data');
            }
        }
    }

    // Sla form data op
    function saveFormData() {
        var formData = {};

        $('.wpcf7-form').find('input, select, textarea').each(function() {
            var $field = $(this);
            var name = $field.attr('name');
            var value = $field.val();

            if (name && value && $field.attr('type') !== 'submit') {
                formData[name] = value;
            }
        });

        if (Object.keys(formData).length > 0) {
            localStorage.setItem(autoSaveKey, JSON.stringify(formData));
        }
    }

    // Luister naar form wijzigingen
    $(document).on('change input', '.wpcf7-form input, .wpcf7-form select, .wpcf7-form textarea', function() {
        saveFormData();
    });

    // Wis opgeslagen data na succesvolle verzending
    $(document).on('wpcf7mailsent', function() {
        localStorage.removeItem(autoSaveKey);
        console.log('Workshop form data gewist na succesvolle verzending');
    });

    // Laad data bij initialisatie
    setTimeout(loadSavedData, 500);
}

// Update de initWorkshopForm functie
function initWorkshopForm() {
    // Bestaande functionaliteit
    enhanceWorkshopSelect();
    addWorkshopFormValidation();
    improveUserExperience();

    // Nieuwe functionaliteit
    addWorkshopTooltips();
    improveFormAccessibility();
    addFormAnalytics();
    addAutoSave();
}
