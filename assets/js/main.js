var pageContainerWrap = null; // Initialiseer als null
var scroller;
var scrollValues = {};
var dynamicScripts = [];
var popState = false;
var inlineStyles = null;
var containerWidth = $(window).width();

// Font loading
document.fonts.ready.then(function(){

  if ('scrollRestoration' in history) {
    history.scrollRestoration = 'manual';
  }

  updateDynamicScriptsArray();

  pageContainerWrap = new Swup({
    cache: true,
    containers: ["#pageContainer, .languageSelector"],
    animateHistoryBrowsing: true,
    plugins: [
      new SwupHeadPlugin({
        persistAssets: true,
        persistTags: 'style link',
      })
      // SwupGtagPlugin uitgeschakeld om warnings te voorkomen
      // new SwupGtagPlugin({
      //   gaMeasurementId: '',
      // })
    ]
  });

  pageContainerWrap.on('clickLink', () => {
    scrollValues[window.location.href] = window.scrollY;
  });

  pageContainerWrap.on('popState', () => {
    popState = true;
    $(document).one("initPage", function(){
      if (popState) {
        window.scrollTo(0, scrollValues[window.location.href]);
        popState = false;
      }
    });
  });

  setTimeout(preloadPage, 100);

  pageContainerWrap.on('willReplaceContent', () => {
    inlineStyles = $("head style").clone();
  });

  pageContainerWrap.on('pageView', () => {
    dynamicScriptLoad();
    updateDynamicScriptsArray();
    if (inlineStyles) {
      $("head").append(inlineStyles);
      inlineStyles = null;
    }
    setTimeout(initPage, 100);
  });

  pageContainerWrap.on('animationOutDone', () => {
    scroller.scrollTo(0, { offset: 0, duration: 0, easing: "linear", immediate: true });
    $("header").removeClass("scrolled scrollDown");
    scroller.stop();
    scroller.start();
    $("html").addClass("stopScroll");
  });

  pageContainerWrap.on('willReplaceContent', () => {
    $("html").addClass("stopScroll");
  });

  $(window).resize(function() {
    containerWidth = $(window).width();
  });

});

function updateDynamicScriptsArray() {
  $("head script").each(function() {
    const src = $(this).attr("src");
    if (!dynamicScripts.includes(src)) {
      dynamicScripts.push(src);
    }
  });
}

function dynamicScriptLoad() {
  $("head script").each(function() {
    const src = $(this).attr("src");
    if (!dynamicScripts.includes(src)) {
      const scriptEle = document.createElement("script");
      scriptEle.setAttribute("src", src);
      $(this).remove();
      document.head.appendChild(scriptEle);
    }
  });
}

function initLenis() {
  scroller = new Lenis({
    duration: 1.2,
    easing: t => Math.min(1, 1.001 - Math.pow(2, -10 * t)),
    orientation: 'vertical',
    gestureOrientation: 'vertical',
    smoothWheel: true,
    smoothTouch: false,
  });

  scroller.on("scroll", function(e) {
    checkInviewClasses();
    $("header").toggleClass("scrolled", e.scrollY > 0);
    $("header").toggleClass("scrollDown", e.direction === 1);
    $("header").toggleClass("dark", shouldSetHeaderDark());
  });
}

function shouldSetHeaderDark() {
  return $(".changeHeaderToDark").toArray().some(el => {
    const $el = $(el);
    const offset = $el.offset().top - $("header .innerBar .right a").offset().top - ($("header .innerBar .right a").height() / 2);
    return offset <= 0 && offset >= -Math.abs($el.outerHeight());
  });
}

function preloadPage() {
  initLenis();
  initPage();
  currentScrollY = $(window).scrollTop();
  scroller.on("scroll", () => currentScrollY = $(window).scrollTop());

  setTimeout(function(){
    ScrollTrigger.update();
  }, 100);
  $(".select2").select2({ minimumResultsForSearch: -1 });
  scroller.on('scroll', ScrollTrigger.update)

  gsap.ticker.add((time)=>{
    scroller.raf(time * 1000)
  })

  gsap.ticker.lagSmoothing(0)

  setTimeout(() => {
    $("html, body").removeClass("overflow");
    $(".content").removeClass("fade");
    $("header").addClass("active");
    // Zorg ervoor dat CF7 ook bij eerste load wordt geïnitialiseerd
    initContactForm7();
  }, 300);
}

function initPage() {
  $("html").removeClass("stopScroll fade");
  // Reset CF7 formulieren EERST voordat de pagina wordt geïnitialiseerd
  resetContactForm7();
  $(document).trigger("initPage");
  $(".select2").select2({ minimumResultsForSearch: -1 });
  splitIt();
  checkInviewClasses();
  checkParallaxItems();
  lazyLoadImages();
  scrollDownClick();
  initContactForm7();
}

function resetContactForm7() {
  // Controleer of er CF7 formulieren zijn
  if ($('.wpcf7-form').length === 0) {
    return;
  }

  // Reset alle CF7 formulieren
  $('.wpcf7-form').each(function() {
    var form = $(this);

    // Verwijder alle error/success classes en berichten
    form.removeClass('invalid spam sent failed');
    form.find('.wpcf7-not-valid').removeClass('wpcf7-not-valid');
    form.find('.wpcf7-not-valid-tip').remove();
    form.find('.wpcf7-response-output').remove();

    // Reset alleen de zichtbare velden, NIET de hidden CF7 velden
    form.find('input:not([type="hidden"]):not([type="submit"]), textarea, select').each(function() {
      $(this).removeClass('wpcf7-not-valid');
      $(this).val('');
    });

    // Reset file inputs
    form.find('input[type="file"]').val('');

    // Reset checkboxes en radio buttons
    form.find('input[type="checkbox"], input[type="radio"]').prop('checked', false);

    // Reset form status
    form.attr('data-status', 'init');
  });
}

function initContactForm7() {
  // Controleer of er CF7 formulieren zijn
  if ($('.wpcf7-form').length === 0) {
    return;
  }

  // In plaats van CF7 opnieuw te initialiseren, zorgen we ervoor dat de formulieren correct werken
  setTimeout(function() {
    $('.wpcf7-form').each(function() {
      var form = $(this);

      // Zorg ervoor dat het formulier de juiste action heeft
      var currentUrl = window.location.pathname;
      var formAction = form.attr('action');

      // Update de form action als het niet correct is
      if (formAction && !formAction.includes(currentUrl)) {
        var newAction = currentUrl + (formAction.includes('#') ? '#' + formAction.split('#')[1] : '');
        form.attr('action', newAction);
      }

      // Verwijder oude event handlers en laat CF7 zijn eigen handlers toevoegen
      form.off('submit.wpcf7');

      // Zorg ervoor dat het formulier een unieke ID heeft voor CF7
      if (!form.attr('id')) {
        form.attr('id', 'wpcf7-form-' + Math.random().toString(36).substring(2, 11));
      }
    });

    // Trigger CF7 ready event voor plugins die hierop luisteren
    $(document).trigger('wpcf7:ready');
  }, 100);
}

function lazyLoadImages() {
  if ("IntersectionObserver" in window) {
    const config = {
      root: null,
      rootMargin: '1000px',
      threshold: 0.0
    };
    const imageObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const image = entry.target;
          image.classList.remove("lazy");
          image.src = $(image).data('src');
          imageObserver.unobserve(image);
        }
      });
    }, config);

    document.querySelectorAll(".lazy, .lazyload").forEach(image => {
      imageObserver.observe(image);
    });
  } else {
    $(".lazy").each(function() {
      $(this).removeClass("lazy");
      $(this).attr('src', $(this).data('src'));
    });
  }
}

function splitIt() {
  $(".splitThis").each(function(i, e) {
    $(e).splitLines({
      tag: "<div class='wrapper'><span class='innerWrapper'>",
      keepHtml: true
    });
    setTimeout(() => $(e).addClass("animate"), i * 200);
  });
}

function checkInviewClasses() {
  $("[data-init]").each(function() {
    const scrollDirect = $(this).data("scroll-direct");
    ScrollTrigger.create({
      trigger: this,
      start: scrollDirect ? "0% 100%" : "0% 90%",
      end: "0% 90%",
      onEnter: () => $(this).addClass("inview")
    });
  });
}

function checkParallaxItems() {
  $('[data-scroll]').each(function() {
    const speed = $(this).data("scroll-speed");
    if (speed) {
      gsap.to(this, 0.15, {
        y: speed * 30,
        scrollTrigger: {
          trigger: $(this).parents("section"),
          start: 'top bottom',
          end: 'bottom top',
          scrub: true
        }
      });
    }
  });
}

function scrollDownClick() {
  $(document).off("click.scrollDownButton").on("click.scrollDownButton", "[data-scroll-down]", function() {
    scroller.scrollTo("section:nth-child(2)");
  });
}