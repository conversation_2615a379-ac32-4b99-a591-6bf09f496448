var menuIsOpen = false;
var defaultMenuWidth;
var defaultMenuHeight;

var container;
var oldHeight;
var newHeight;

var menuDisabled = false;
var menu;
var header;

document.fonts.ready.then(function(){
  menu = $("#menu");
  header = $("header");
  gsap.registerPlugin(CustomEase);
  CustomEase.create(
    "menubackground",
    "M0,0 C0.83,0 0.17,1 1,1"
  );

  defaultMenuWidth = $("header #menu").outerWidth();
  defaultMenuHeight = $("header #menu").outerHeight();


  // gsap.to("header #menu .background", 0, {
  //   width: defaultMenuWidth,
  //   height: defaultMenuHeight
  // });
  //
  // $(window).on("resize", function(){
  //   defaultMenuWidth = $("header #menu").outerWidth();
  //   defaultMenuHeight = $("header #menu").outerHeight();
  //
  //   gsap.to("header #menu .background", 0, {
  //     width: $("header #menu").outerWidth(),
  //     height: $("header #menu").outerHeight()
  //   });
  // });

  $(document).off("click.menuToggle", "header .menuToggle").on("click.menuToggle", "header .menuToggle", function(e){
    if (!menuDisabled) {
      menuDisabled = true;
      if (menuIsOpen) {
        menuIsOpen = false;
        closeMenu();
      } else {
        openMenu();
        menuIsOpen = true;
      }
    }
  });


  setTimeout(function(){
    scroller.on("scroll", function(){
      if (!menuDisabled) {
        if (menuIsOpen) {
          menuIsOpen = false;
          closeMenu("header #menu");
        }
      }
    });
  }, 400);

  $(document).on("click", function(e){
    if (!menuDisabled) {
      if (menuIsOpen) {
        menuIsOpen = false;
        closeMenu();
      }
    }
  });

  $(document).on("mouseover", "#menu .title li", function(e){
    var item = $(e)[0].currentTarget;
    $(item).parents("ul").find("li").removeClass("active");
    $(item).parents("ul").addClass("hover");
    $(item).addClass("active");
  });

  $(document).on("mouseleave", "#menu .title li", function(e){
    var item = $(e)[0].currentTarget;
    $(item).parents("ul").find("li").removeClass("active");
    $(item).parents("ul").removeClass("hover");
  });
});

function openMenu() {
  $(menu).toggleClass("active");

  gsap.to($(header), .75, {
    y: -$(header).outerHeight(),
    ease: "menubackground"
  });
  scroller.stop();
  setTimeout(function(){
    $(menu).find(".innerContent").addClass("showContent");
    $(header).toggleClass("menuOpen");
    gsap.to($(header), .6, {
      y: 0,
      ease: "menubackground"
    });
  }, 600);
  setTimeout(function(){
    menuDisabled = false;
  }, 900);
}

function closeMenu() {
  scroller.start();
  $(menu).find(".innerContent").removeClass("showContent");
  $(header).toggleClass("menuOpen");
  $(menu).toggleClass("active");

  gsap.to($(header), .75, {
    y: -$(header).outerHeight(),
    ease: "menubackground"
  });
  setTimeout(function(){
    gsap.to($(header), .6, {
      y: 0,
      ease: "menubackground"
    });
  }, 600);
  setTimeout(function(){
    menuDisabled = false;
  }, 900);
}
