<!DOCTYPE html>
<html lang="nl" dir="ltr">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title><?php echo get_theme_mod('customTheme-main-callout-title') ? get_theme_mod('customTheme-main-callout-title') : get_bloginfo('name'); ?> | <?php the_title(); ?></title>
    <meta name="robots" content="follow, index, max-snippet:-1, max-video-preview:-1, max-image-preview:large">
    <meta name="msapplication-TileColor" content="#00aba9">
    <meta name="theme-color" content="#ffffff">
    <meta name="description" content="<?php echo esc_attr(get_theme_mod('customTheme-main-callout-description')); ?>">
    <meta name="author" content="https://www.linkedin.com/in/dennisthemenace/"/>
    <meta property="og:title" content="<?php echo get_theme_mod('customTheme-main-callout-title') ? get_theme_mod('customTheme-main-callout-title') : get_bloginfo('name'); ?> | <?php the_title(); ?>" />
    <meta property="og:description" content="<?php echo esc_attr(get_theme_mod('customTheme-main-callout-description')); ?>" />
    <meta property="og:image" content="<?php echo esc_url(wp_get_attachment_url(get_theme_mod('customTheme-main-callout-featured-image'))); ?>" />
    <meta property="og:image:alt" content="<?php echo esc_attr(get_theme_mod('customTheme-main-callout-title')); ?>" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="<?php echo esc_url(get_permalink()); ?>" />
    <meta property="og:site_name" content="<?php echo get_theme_mod('customTheme-main-callout-title') ? get_theme_mod('customTheme-main-callout-title') : get_bloginfo('name'); ?>" />
    <meta property="og:locale" content="nl" />
    <link rel="canonical" href="<?php echo esc_url(get_permalink()); ?>" />
    <link rel="icon" href="<?php echo get_stylesheet_directory_uri(); ?>/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="<?php echo get_stylesheet_directory_uri(); ?>/favicon.ico" type="image/x-icon">
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Organization",
      "name": "<?php echo get_bloginfo('name'); ?>",
      "url": "<?php echo esc_url(home_url()); ?>",
      "logo": "<?php echo get_stylesheet_directory_uri(); ?>/logo.png",
      "contactPoint": {
        "@type": "ContactPoint",
        "telephone": "<?php echo esc_html(get_theme_mod('customTheme-main-callout-telephone')); ?>",
        "contactType": "customer service"
      }
    }
    </script>
    <?php wp_head(); ?>
  </head>
  <?php if(get_theme_mod("customTheme-main-callout-analytics")) { ?>
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=<?php echo get_theme_mod('customTheme-main-callout-analytics') ?>"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', '<?php echo get_theme_mod('customTheme-main-callout-analytics') ?>', {
        'anonymize_ip': true
      });
    </script>
  <?php } ?>
  <body class="no-scroll">
    <header data-init>
      <div class="contentWrapper">
        <div class="col">
          <a href="/" title="Logo | <?php echo get_theme_mod('customTheme-main-callout-title') ?>" class="logo">
            <svg width="256" height="65.267" viewBox="0 0 256 65.267">
              <g data-name="Group 175" transform="translate(-4731 195.267)">
                <rect data-name="Rectangle 1" width="256" height="65.267" transform="translate(4731 -195.267)" fill="none"/>
                <path data-name="Path 9" d="M763.269,519.789a7.283,7.283,0,0,1-3.1-2.449,6.219,6.219,0,0,1-1.128-3.7h4.3a3.876,3.876,0,0,0,1.251,2.607,4.432,4.432,0,0,0,3.118,1.021,4.8,4.8,0,0,0,3.242-1,3.262,3.262,0,0,0,1.163-2.589,2.831,2.831,0,0,0-.722-2.008,4.843,4.843,0,0,0-1.8-1.2,27.378,27.378,0,0,0-2.977-.916,27.763,27.763,0,0,1-3.894-1.286,6.432,6.432,0,0,1-2.554-2.026,5.854,5.854,0,0,1-1.057-3.665,6.512,6.512,0,0,1,1.057-3.7,6.751,6.751,0,0,1,2.96-2.431,10.753,10.753,0,0,1,4.4-.846,9.18,9.18,0,0,1,5.831,1.779,6.566,6.566,0,0,1,2.52,4.88h-4.44a3.219,3.219,0,0,0-1.269-2.29,4.688,4.688,0,0,0-3.065-.952,4.349,4.349,0,0,0-2.818.881,3.07,3.07,0,0,0-1.093,2.537,2.574,2.574,0,0,0,.687,1.85,4.849,4.849,0,0,0,1.744,1.145,27.117,27.117,0,0,0,2.89.916,30.218,30.218,0,0,1,3.964,1.339,6.612,6.612,0,0,1,2.607,2.061,5.928,5.928,0,0,1,1.075,3.717,6.684,6.684,0,0,1-1,3.524,7.236,7.236,0,0,1-2.924,2.66,9.643,9.643,0,0,1-4.528,1,10.965,10.965,0,0,1-4.439-.863" transform="translate(4035.951 -667.271)" fill="#589552"/>
                <path data-name="Path 10" d="M844.168,529.3a5.64,5.64,0,0,0,2.2-2.219,7.139,7.139,0,0,0,.846-3.594,7.282,7.282,0,0,0-.811-3.576,5.476,5.476,0,0,0-2.149-2.2,5.883,5.883,0,0,0-2.889-.74,5.778,5.778,0,0,0-2.871.74,5.282,5.282,0,0,0-2.1,2.2,7.559,7.559,0,0,0-.775,3.576,6.812,6.812,0,0,0,1.6,4.845,5.3,5.3,0,0,0,4.034,1.709,5.977,5.977,0,0,0,2.907-.74m-7.875,2.977a8.983,8.983,0,0,1-3.489-3.523,10.635,10.635,0,0,1-1.268-5.268,10.423,10.423,0,0,1,1.3-5.25,9.008,9.008,0,0,1,3.558-3.524,10.911,10.911,0,0,1,10.077,0,9.011,9.011,0,0,1,3.559,3.524,10.428,10.428,0,0,1,1.3,5.25,10.211,10.211,0,0,1-1.338,5.25,9.275,9.275,0,0,1-3.648,3.541,10.506,10.506,0,0,1-5.091,1.251,9.937,9.937,0,0,1-4.968-1.251" transform="translate(3984.032 -680.074)" fill="#589552"/>
                <path data-name="Path 11" d="M918.01,514.537h-4.016V495.123h4.016Zm-3.8-22.726a2.572,2.572,0,0,1,1.832-4.4,2.445,2.445,0,0,1,1.8.74,2.638,2.638,0,0,1,0,3.664,2.445,2.445,0,0,1-1.8.74,2.486,2.486,0,0,1-1.832-.74" transform="translate(3925.357 -661.404)" fill="#589552"/>
                <rect data-name="Rectangle 2" width="256" height="65.267" transform="translate(4731 -195.267)" fill="none"/>
                <rect data-name="Rectangle 3" width="4.016" height="26.073" transform="translate(4848.652 -172.94)" fill="#589552"/>
                <path data-name="Path 12" d="M1008.48,514.428a6.872,6.872,0,0,1,2.854,2.819,9.149,9.149,0,0,1,1.04,4.51v11.451h-3.982V522.356a5.636,5.636,0,0,0-1.3-4,4.642,4.642,0,0,0-3.559-1.392,4.7,4.7,0,0,0-3.576,1.392,5.589,5.589,0,0,0-1.321,4v10.852H994.65V522.356a5.636,5.636,0,0,0-1.3-4,4.639,4.639,0,0,0-3.558-1.392,4.7,4.7,0,0,0-3.576,1.392,5.587,5.587,0,0,0-1.321,4v10.852h-4.017V513.794h4.017v2.22a6.671,6.671,0,0,1,2.5-1.868,7.928,7.928,0,0,1,3.241-.669,8.637,8.637,0,0,1,4.158.986,6.753,6.753,0,0,1,2.819,2.854,6.508,6.508,0,0,1,2.748-2.8,8.128,8.128,0,0,1,4.016-1.039,8.706,8.706,0,0,1,4.1.951" transform="translate(3877.081 -680.074)" fill="#589552"/>
                <path data-name="Path 13" d="M1120.2,519.994a5.815,5.815,0,0,0-2.166-2.255,5.676,5.676,0,0,0-2.889-.775,5.774,5.774,0,0,0-2.889.757,5.712,5.712,0,0,0-2.167,2.22,6.926,6.926,0,0,0-.828,3.47,7.226,7.226,0,0,0,.828,3.523,5.836,5.836,0,0,0,2.184,2.308,5.6,5.6,0,0,0,2.872.793,5.671,5.671,0,0,0,2.889-.775,5.781,5.781,0,0,0,2.166-2.273,7.131,7.131,0,0,0,.828-3.506,7.031,7.031,0,0,0-.828-3.488m-13.847-1.762a8.856,8.856,0,0,1,7.945-4.757,8.476,8.476,0,0,1,4,.9,8.117,8.117,0,0,1,2.73,2.237v-2.819h4.052v19.414h-4.052v-2.889a7.978,7.978,0,0,1-2.784,2.29,9.009,9.009,0,0,1-8.6-.37,9.119,9.119,0,0,1-3.294-3.594,11.054,11.054,0,0,1-1.216-5.232,10.743,10.743,0,0,1,1.216-5.179" transform="translate(3788.089 -680.074)" fill="#589552"/>
                <path data-name="Path 14" d="M1207.917,514.428a6.77,6.77,0,0,1,2.836,2.819,9.277,9.277,0,0,1,1.021,4.51v11.451h-3.981V522.356a5.635,5.635,0,0,0-1.3-4,4.639,4.639,0,0,0-3.558-1.392,4.7,4.7,0,0,0-3.577,1.392,5.587,5.587,0,0,0-1.321,4v10.852h-4.017V513.794h4.017v2.22a6.644,6.644,0,0,1,2.519-1.868,8.056,8.056,0,0,1,3.259-.669,8.7,8.7,0,0,1,4.1.951" transform="translate(3724.433 -680.074)" fill="#589552"/>
                <path data-name="Path 15" d="M1277.264,514.537h-4.016V495.123h4.016Zm-3.805-22.726a2.572,2.572,0,0,1,1.832-4.4,2.446,2.446,0,0,1,1.8.74,2.639,2.639,0,0,1,0,3.664,2.445,2.445,0,0,1-1.8.74,2.487,2.487,0,0,1-1.832-.74" transform="translate(3668.07 -661.404)" fill="#589552"/>
                <path data-name="Path 16" d="M1317.7,519.994a5.817,5.817,0,0,0-2.166-2.255,5.678,5.678,0,0,0-2.89-.775,5.773,5.773,0,0,0-2.889.757,5.713,5.713,0,0,0-2.167,2.22,6.924,6.924,0,0,0-.828,3.47,7.224,7.224,0,0,0,.828,3.523,5.836,5.836,0,0,0,2.184,2.308,5.6,5.6,0,0,0,2.871.793,5.672,5.672,0,0,0,2.89-.775,5.783,5.783,0,0,0,2.166-2.273,7.133,7.133,0,0,0,.828-3.506,7.034,7.034,0,0,0-.828-3.488m-13.847-1.762a8.855,8.855,0,0,1,7.945-4.757,8.477,8.477,0,0,1,4,.9,8.115,8.115,0,0,1,2.73,2.237v-2.819h4.052v19.414h-4.052v-2.889a7.972,7.972,0,0,1-2.784,2.29,9.009,9.009,0,0,1-8.6-.37,9.117,9.117,0,0,1-3.294-3.594,11.051,11.051,0,0,1-1.216-5.232,10.74,10.74,0,0,1,1.216-5.179" transform="translate(3646.643 -680.074)" fill="#589552"/>
                <path data-name="Path 17" d="M631.557,530.4l-.023,3.075-21.317-.154a8.669,8.669,0,0,0,1.376-3.067Z" transform="translate(4142.535 -692.093)" fill="#589552"/>
                <path data-name="Path 18" d="M565.249,560.946a6.738,6.738,0,0,0,.192,1.668L547,562.476l.023-3.075,18.388.138a6.084,6.084,0,0,0-.161,1.407" transform="translate(4187.809 -712.964)" fill="#589552"/>
                <path data-name="Path 19" d="M665.68,560.275l-.023,3.075-7.934-.062a6.92,6.92,0,0,0,.223-1.66,6.593,6.593,0,0,0-.146-1.415Z" transform="translate(4108.512 -713.545)" fill="#589552"/>
                <path data-name="Path 20" d="M628.869,555.029v.028a3.978,3.978,0,0,1-3.965,3.931h-.028a3.919,3.919,0,0,1-2.794-1.178,3.865,3.865,0,0,1-.853-1.311,3.912,3.912,0,0,1-.284-1.5,3.687,3.687,0,0,1,.222-1.275,3.959,3.959,0,0,1,3.737-2.662h.028a3.959,3.959,0,0,1,3.729,2.717,3.735,3.735,0,0,1,.208,1.248" transform="translate(4134.851 -706.993)" fill="#589552"/>
                <path data-name="Path 21" d="M573.574,509.25a5.664,5.664,0,0,1-.261,1.641v.007a5.6,5.6,0,0,1-1.765,2.6l-.007.007a5.527,5.527,0,0,1-3.523,1.262h-.04a5.445,5.445,0,0,1-3.543-1.314l-1.2-1.405v-.007a5.551,5.551,0,0,1,4.779-8.374h.046a5.558,5.558,0,0,1,5.511,5.589" transform="translate(4176.731 -673.045)" fill="#589552"/>
                <path data-name="Path 22" d="M565.186,533.188a5.6,5.6,0,0,0,.824,1.066,3.683,3.683,0,0,0,.373.34Z" transform="translate(4174.784 -694.191)" fill="#a0ca97"/>
                <path data-name="Path 23" d="M588.446,585.127a3.53,3.53,0,0,1-3.521,3.5h-.031a3.532,3.532,0,0,1-3.5-3.551,3.531,3.531,0,0,1,3.521-3.49h.031a3.518,3.518,0,0,1,3.5,3.544" transform="translate(4163.175 -728.85)" fill="#589552"/>
                <path data-name="Path 24" d="M626.327,457.173a16.738,16.738,0,0,1-13.107,3.844c2.614-5.9,9.456-12.869,9.456-12.869-5.158,3.244-9.663,9.248-11.354,11.67-.084.123-.162.231-.23.331a14.481,14.481,0,0,1-.454-1.76c-1.368-6.527,1.645-11.485,5.051-14.729a17.7,17.7,0,0,1,15.1-4.382,2.08,2.08,0,0,1,1.169.477,2.2,2.2,0,0,1,.508,1.507c.361,6.1-1.807,12.138-6.135,15.913" transform="translate(4142.475 -626.749)" fill="#589552"/>
                <path data-name="Path 25" d="M1047.01,613.693a.932.932,0,0,0,.318-.752.977.977,0,0,0-.337-.78,1.33,1.33,0,0,0-.9-.289h-1.445v2.091h1.484a1.314,1.314,0,0,0,.876-.27m-2.36-2.716h1.358a1.306,1.306,0,0,0,.843-.246.858.858,0,0,0,.3-.708.873.873,0,0,0-.3-.708,1.27,1.27,0,0,0-.843-.256h-1.358Zm3.4,1.011a1.718,1.718,0,0,1,.111,1.989,1.793,1.793,0,0,1-.766.646,2.7,2.7,0,0,1-1.166.236h-2.678v-6.695h2.553a2.738,2.738,0,0,1,1.175.231,1.723,1.723,0,0,1,.742.621,1.586,1.586,0,0,1,.25.872,1.494,1.494,0,0,1-.308.963,1.8,1.8,0,0,1-.829.568,1.481,1.481,0,0,1,.915.568" transform="translate(3832.192 -747.887)" fill="#589552"/>
                <path data-name="Path 26" d="M1067.132,617.069a1.539,1.539,0,0,0,.6-.607,1.949,1.949,0,0,0,.231-.983,1.991,1.991,0,0,0-.222-.978,1.5,1.5,0,0,0-.588-.6,1.608,1.608,0,0,0-.79-.2,1.58,1.58,0,0,0-.785.2,1.445,1.445,0,0,0-.573.6,2.069,2.069,0,0,0-.212.978,1.861,1.861,0,0,0,.438,1.325,1.448,1.448,0,0,0,1.1.467,1.632,1.632,0,0,0,.794-.2m-2.152.814a2.454,2.454,0,0,1-.954-.964,2.9,2.9,0,0,1-.347-1.44,2.854,2.854,0,0,1,.356-1.436,2.461,2.461,0,0,1,.973-.962,2.975,2.975,0,0,1,2.755,0,2.461,2.461,0,0,1,.973.962,2.852,2.852,0,0,1,.357,1.436,2.792,2.792,0,0,1-.366,1.435,2.537,2.537,0,0,1-1,.968,2.87,2.87,0,0,1-1.392.342,2.714,2.714,0,0,1-1.358-.342" transform="translate(3817.778 -751.166)" fill="#589552"/>
                <path data-name="Path 27" d="M1088.785,617.069a1.542,1.542,0,0,0,.6-.607,1.951,1.951,0,0,0,.231-.983,1.99,1.99,0,0,0-.222-.978,1.5,1.5,0,0,0-.587-.6,1.609,1.609,0,0,0-.79-.2,1.579,1.579,0,0,0-.785.2,1.444,1.444,0,0,0-.573.6,2.07,2.07,0,0,0-.212.978,1.861,1.861,0,0,0,.438,1.325,1.448,1.448,0,0,0,1.1.467,1.632,1.632,0,0,0,.794-.2m-2.153.814a2.449,2.449,0,0,1-.954-.964,2.9,2.9,0,0,1-.347-1.44,2.851,2.851,0,0,1,.357-1.436,2.458,2.458,0,0,1,.973-.962,2.976,2.976,0,0,1,2.755,0,2.463,2.463,0,0,1,.973.962,2.857,2.857,0,0,1,.356,1.436,2.793,2.793,0,0,1-.366,1.435,2.535,2.535,0,0,1-1,.968,2.87,2.87,0,0,1-1.392.342,2.715,2.715,0,0,1-1.358-.342" transform="translate(3802.271 -751.166)" fill="#589552"/>
                <path data-name="Path 28" d="M1108.315,618a1.978,1.978,0,0,1-.785-.617,1.541,1.541,0,0,1-.308-.872h1.137a.773.773,0,0,0,.323.564,1.169,1.169,0,0,0,.737.226,1.238,1.238,0,0,0,.718-.179.537.537,0,0,0,.255-.457.47.47,0,0,0-.285-.444,5.364,5.364,0,0,0-.9-.318,8.467,8.467,0,0,1-.973-.318,1.7,1.7,0,0,1-.65-.472,1.237,1.237,0,0,1-.275-.838,1.308,1.308,0,0,1,.25-.775,1.663,1.663,0,0,1,.718-.554,2.663,2.663,0,0,1,1.074-.2,2.217,2.217,0,0,1,1.459.458,1.643,1.643,0,0,1,.593,1.247h-1.1a.785.785,0,0,0-.289-.568,1.081,1.081,0,0,0-.7-.212,1.144,1.144,0,0,0-.665.164.507.507,0,0,0-.231.433.469.469,0,0,0,.154.357,1.084,1.084,0,0,0,.376.226q.222.082.655.208a7.1,7.1,0,0,1,.949.313,1.766,1.766,0,0,1,.641.467,1.235,1.235,0,0,1,.28.818,1.374,1.374,0,0,1-.251.81,1.661,1.661,0,0,1-.708.558,2.642,2.642,0,0,1-1.074.2,2.684,2.684,0,0,1-1.122-.226" transform="translate(3786.594 -751.167)" fill="#589552"/>
                <path data-name="Path 29" d="M1126.5,610.617v2.938a.564.564,0,0,0,.14.429.7.7,0,0,0,.477.129h.674v.915h-.867a1.672,1.672,0,0,1-1.137-.347,1.429,1.429,0,0,1-.395-1.127v-2.938h-.626v-.9h.626V608.4h1.108v1.319h1.291v.9Z" transform="translate(3774.026 -748.057)" fill="#589552"/>
                <path data-name="Path 30" d="M1139.724,613.036h-1.1v-5.308h1.1Zm-1.041-6.214a.7.7,0,0,1,.5-1.2.669.669,0,0,1,.491.2.722.722,0,0,1,0,1,.669.669,0,0,1-.491.2.681.681,0,0,1-.5-.2" transform="translate(3764.207 -746.063)" fill="#589552"/>
                <path data-name="Path 31" d="M1151.749,613a1.85,1.85,0,0,1,.776.771,2.535,2.535,0,0,1,.28,1.233v3.131h-1.089v-2.967a1.542,1.542,0,0,0-.356-1.093,1.271,1.271,0,0,0-.973-.38,1.287,1.287,0,0,0-.978.38,1.53,1.53,0,0,0-.361,1.093v2.967h-1.1v-5.308h1.1v.607a1.824,1.824,0,0,1,.688-.51,2.2,2.2,0,0,1,.891-.183,2.38,2.38,0,0,1,1.122.26" transform="translate(3757.426 -751.167)" fill="#589552"/>
                <path data-name="Path 32" d="M1172.805,614.527a1.6,1.6,0,0,0-.593-.617,1.55,1.55,0,0,0-.79-.212,1.558,1.558,0,0,0-1.383.814,1.9,1.9,0,0,0-.226.95,1.975,1.975,0,0,0,.226.962,1.591,1.591,0,0,0,.6.631,1.526,1.526,0,0,0,.785.217,1.542,1.542,0,0,0,.79-.212,1.584,1.584,0,0,0,.593-.621,1.956,1.956,0,0,0,.226-.959,1.926,1.926,0,0,0-.226-.953m-.521-1.537a2.112,2.112,0,0,1,.747.612v-.771h1.107v5.395a2.71,2.71,0,0,1-.308,1.305,2.224,2.224,0,0,1-.891.9,2.794,2.794,0,0,1-1.392.328,3.02,3.02,0,0,1-1.792-.506,1.863,1.863,0,0,1-.808-1.373h1.088a1.182,1.182,0,0,0,.535.669,1.81,1.81,0,0,0,.978.256,1.461,1.461,0,0,0,1.075-.405,1.581,1.581,0,0,0,.409-1.175v-.886a2.281,2.281,0,0,1-1.84.886,2.377,2.377,0,0,1-1.267-.351,2.476,2.476,0,0,1-.905-.983,3.019,3.019,0,0,1-.333-1.43,2.941,2.941,0,0,1,.333-1.417,2.422,2.422,0,0,1,2.172-1.3,2.355,2.355,0,0,1,1.093.246" transform="translate(3742.574 -751.167)" fill="#589552"/>
                <path data-name="Path 33" d="M1201.886,618a1.976,1.976,0,0,1-.785-.617,1.54,1.54,0,0,1-.308-.872h1.137a.772.772,0,0,0,.323.564,1.169,1.169,0,0,0,.737.226,1.24,1.24,0,0,0,.718-.179.537.537,0,0,0,.255-.457.47.47,0,0,0-.284-.444,5.394,5.394,0,0,0-.9-.318,8.485,8.485,0,0,1-.974-.318,1.694,1.694,0,0,1-.65-.472,1.236,1.236,0,0,1-.275-.838,1.309,1.309,0,0,1,.251-.775,1.666,1.666,0,0,1,.718-.554,2.665,2.665,0,0,1,1.074-.2,2.217,2.217,0,0,1,1.459.458,1.644,1.644,0,0,1,.593,1.247h-1.1a.785.785,0,0,0-.289-.568,1.081,1.081,0,0,0-.7-.212,1.143,1.143,0,0,0-.665.164.507.507,0,0,0-.231.433.468.468,0,0,0,.154.357,1.088,1.088,0,0,0,.376.226q.222.082.655.208a7.126,7.126,0,0,1,.949.313,1.768,1.768,0,0,1,.64.467,1.236,1.236,0,0,1,.28.818,1.371,1.371,0,0,1-.251.81,1.661,1.661,0,0,1-.708.558,2.642,2.642,0,0,1-1.074.2,2.685,2.685,0,0,1-1.122-.226" transform="translate(3719.581 -751.167)" fill="#589552"/>
                <path data-name="Path 34" d="M1222.133,617.069a1.541,1.541,0,0,0,.6-.607,1.951,1.951,0,0,0,.232-.983,1.988,1.988,0,0,0-.222-.978,1.491,1.491,0,0,0-.587-.6,1.608,1.608,0,0,0-.79-.2,1.58,1.58,0,0,0-.785.2,1.446,1.446,0,0,0-.573.6,2.072,2.072,0,0,0-.212.978,1.861,1.861,0,0,0,.438,1.325,1.448,1.448,0,0,0,1.1.467,1.632,1.632,0,0,0,.795-.2m-2.152.814a2.448,2.448,0,0,1-.954-.964,2.9,2.9,0,0,1-.347-1.44,2.854,2.854,0,0,1,.356-1.436,2.462,2.462,0,0,1,.973-.962,2.976,2.976,0,0,1,2.755,0,2.46,2.46,0,0,1,.973.962,2.851,2.851,0,0,1,.357,1.436,2.788,2.788,0,0,1-.366,1.435,2.532,2.532,0,0,1-1,.968,2.872,2.872,0,0,1-1.392.342,2.715,2.715,0,0,1-1.358-.342" transform="translate(3706.771 -751.166)" fill="#589552"/>
                <path data-name="Path 35" d="M1242.321,613.036h-1.1v-5.308h1.1Zm-1.04-6.214a.7.7,0,0,1,.5-1.2.669.669,0,0,1,.491.2.722.722,0,0,1,0,1,.669.669,0,0,1-.491.2.68.68,0,0,1-.5-.2" transform="translate(3690.729 -746.063)" fill="#589552"/>
                <rect data-name="Rectangle 4" width="256" height="65.267" transform="translate(4731 -195.267)" fill="none"/>
                <rect data-name="Rectangle 5" width="1.098" height="7.128" transform="translate(4934.496 -140.156)" fill="#589552"/>
                <path data-name="Path 36" d="M1272.161,608.631a1.849,1.849,0,0,1,.751.77,2.573,2.573,0,0,1,.275,1.233v3.131H1272.1V610.8a1.544,1.544,0,0,0-.356-1.093,1.271,1.271,0,0,0-.973-.38,1.287,1.287,0,0,0-.978.38,1.53,1.53,0,0,0-.361,1.093v2.967h-1.1v-7.128h1.1v2.436a1.848,1.848,0,0,1,.708-.52,2.361,2.361,0,0,1,.939-.183,2.224,2.224,0,0,1,1.084.26" transform="translate(3671.211 -746.793)" fill="#589552"/>
                <path data-name="Path 37" d="M1293.153,615.008a1.252,1.252,0,0,0-.434-.973,1.551,1.551,0,0,0-1.05-.366,1.41,1.41,0,0,0-.973.361,1.509,1.509,0,0,0-.482.977Zm1.108.887H1290.2a1.469,1.469,0,0,0,.472,1.021,1.488,1.488,0,0,0,1.04.386,1.286,1.286,0,0,0,1.252-.742h1.184a2.387,2.387,0,0,1-.872,1.2,2.553,2.553,0,0,1-1.565.467,2.708,2.708,0,0,1-1.363-.342,2.426,2.426,0,0,1-.944-.963,2.934,2.934,0,0,1-.342-1.44,3.012,3.012,0,0,1,.332-1.44,2.336,2.336,0,0,1,.934-.958,2.777,2.777,0,0,1,1.383-.338,2.707,2.707,0,0,1,1.339.328,2.317,2.317,0,0,1,.915.92,2.772,2.772,0,0,1,.328,1.364,3.422,3.422,0,0,1-.039.539" transform="translate(3656.361 -751.167)" fill="#589552"/>
                <path data-name="Path 38" d="M1314.126,614.527a1.6,1.6,0,0,0-.593-.617,1.551,1.551,0,0,0-.79-.212,1.558,1.558,0,0,0-1.383.814,1.9,1.9,0,0,0-.226.95,1.974,1.974,0,0,0,.226.963,1.594,1.594,0,0,0,.6.631,1.526,1.526,0,0,0,.785.217,1.543,1.543,0,0,0,.79-.212,1.582,1.582,0,0,0,.593-.621,1.956,1.956,0,0,0,.226-.959,1.926,1.926,0,0,0-.226-.953m-3.786-.482a2.424,2.424,0,0,1,2.172-1.3,2.315,2.315,0,0,1,1.093.246,2.224,2.224,0,0,1,.747.612v-.771h1.108v5.308h-1.108v-.79a2.182,2.182,0,0,1-.761.626,2.466,2.466,0,0,1-2.35-.1,2.493,2.493,0,0,1-.9-.983,3.019,3.019,0,0,1-.332-1.43,2.941,2.941,0,0,1,.332-1.417" transform="translate(3641.364 -751.167)" fill="#589552"/>
                <rect data-name="Rectangle 6" width="256" height="65.267" transform="translate(4731 -195.267)" fill="none"/>
                <rect data-name="Rectangle 7" width="1.098" height="7.128" transform="translate(4958.27 -140.156)" fill="#589552"/>
                <path data-name="Path 39" d="M1343.408,610.617v2.938a.566.566,0,0,0,.139.429.7.7,0,0,0,.477.129h.674v.915h-.867a1.671,1.671,0,0,1-1.137-.347,1.428,1.428,0,0,1-.395-1.127v-2.938h-.626v-.9h.626V608.4h1.108v1.319h1.291v.9Z" transform="translate(3618.686 -748.057)" fill="#589552"/>
                <path data-name="Path 40" d="M1359.722,608.631a1.847,1.847,0,0,1,.751.77,2.575,2.575,0,0,1,.275,1.233v3.131h-1.089V610.8a1.542,1.542,0,0,0-.356-1.093,1.271,1.271,0,0,0-.973-.38,1.287,1.287,0,0,0-.978.38,1.531,1.531,0,0,0-.361,1.093v2.967h-1.1v-7.128h1.1v2.436a1.852,1.852,0,0,1,.708-.52,2.36,2.36,0,0,1,.939-.183,2.225,2.225,0,0,1,1.084.26" transform="translate(3608.503 -746.793)" fill="#589552"/>
                <rect data-name="Rectangle 8" width="256" height="65.267" transform="translate(4731 -195.267)" fill="none"/>
              </g>
            </svg>
          </a>
          <a href="/" title="Logo | <?php echo get_theme_mod('customTheme-main-callout-title') ?>" class="logo mobile">
            <svg width="112.784" height="133.388" viewBox="0 0 112.784 133.388">
              <g data-name="Group 175" transform="translate(-4761.216 203.681)">
                <path data-name="Path 2883" d="M670.136,530.667l-.065,8.634-59.855-.432a24.339,24.339,0,0,0,3.864-8.612Z" transform="translate(4201.38 -661.226)" fill="#579552"/>
                <path data-name="Path 2884" d="M598.242,563.74a18.927,18.927,0,0,0,.54,4.684L547,568.035l.065-8.634,51.631.388a17.076,17.076,0,0,0-.454,3.95" transform="translate(4214.217 -667.144)" fill="#579552"/>
                <path data-name="Path 2885" d="M680.064,560.386,680,569.02l-22.276-.173a19.427,19.427,0,0,0,.626-4.662,18.5,18.5,0,0,0-.41-3.972Z" transform="translate(4191.734 -667.309)" fill="#579552"/>
                <path data-name="Path 2886" d="M643.194,562.2v.077a11.171,11.171,0,0,1-11.134,11.037h-.078A11,11,0,0,1,624.138,570a10.851,10.851,0,0,1-2.394-3.68,10.984,10.984,0,0,1-.8-4.2,10.353,10.353,0,0,1,.623-3.581,11.116,11.116,0,0,1,10.492-7.474h.077a11.115,11.115,0,0,1,10.472,7.63,10.49,10.49,0,0,1,.585,3.5" transform="translate(4199.202 -665.451)" fill="#579552"/>
                <path data-name="Path 2887" d="M593.654,519.355a15.9,15.9,0,0,1-.734,4.607v.018a15.723,15.723,0,0,1-4.956,7.287l-.019.018a15.517,15.517,0,0,1-9.893,3.542h-.111a15.289,15.289,0,0,1-9.948-3.69l-3.359-3.946v-.018a15.587,15.587,0,0,1,13.418-23.513h.128a15.605,15.605,0,0,1,15.473,15.694" transform="translate(4211.076 -655.825)" fill="#579552"/>
                <path data-name="Path 2888" d="M601.191,591.534a9.912,9.912,0,0,1-9.886,9.822h-.086a9.916,9.916,0,0,1-9.821-9.972,9.913,9.913,0,0,1,9.886-9.8h.087a9.878,9.878,0,0,1,9.821,9.951" transform="translate(4207.232 -671.648)" fill="#579552"/>
                <path data-name="Path 2889" d="M655.3,489.994a47,47,0,0,1-36.8,10.792c7.339-16.577,26.55-36.133,26.55-36.133-14.484,9.109-27.132,25.967-31.881,32.766-.237.346-.454.648-.647.929a40.646,40.646,0,0,1-1.274-4.943c-3.842-18.327,4.62-32.249,14.181-41.357,10.814-10.318,26.766-14.937,42.392-12.3a5.839,5.839,0,0,1,3.281,1.339c1.123,1.079,1.339,2.741,1.425,4.23,1.014,17.117-5.073,34.083-17.225,44.681" transform="translate(4201.363 -642.698)" fill="#579552"/>
              </g>
            </svg>
          </a>
        </div>
        <div class="col">
          <div class="menuToggle">
            <span class="innerContent">Menu</span><span class="hamburger"></span>
          </div>
          <a href="https://my.soilmania.com/" class="login" target="_blank" title="My Soilmania"><i class="icon-user"></i></a>
          <div class="languageSelector" data-language-switcher>
              <?php echo do_shortcode('[language-switcher]'); ?>
          </div>
          <a href="/contact" class="button" title="plan een demo">Maak een afspraak</a>
        </div>

      </div>
    </header>
    <div class="menu" id="menu">
      <div class="background"></div>
      <div class="content">
        <div class="contentWrapper">
          <div class="cols">
            <div class="col">
              <div class="title">
                <?php wp_nav_menu( array(
                  'menu' => 'primary-menu'
                ) ); ?>
              </div>
            </div>
            <div class="col">
              <div class="title">
                <?php wp_nav_menu( array(
                  'menu' => 'secondary-menu'
                ) ); ?>
              </div>
            </div>
          </div>
          <div class="cols">
            <div class="col">
              <div class="normalTitle">
                <?php wp_nav_menu( array(
                  'menu' => 'third-footer-menu',
                ) ); ?>
              </div>
            </div>
            <div class="col">
              <div class="contactLinks">
                <a class="contactLink" title="<?php echo get_theme_mod('customTheme-main-callout-mail') ?>" href="mailto:<?php echo get_theme_mod('customTheme-main-callout-mail') ?>"><span class="icon"><i class="icon-envelope"></i></span><span class="innerText"><?php echo get_theme_mod('customTheme-main-callout-mail') ?></span></a>
                <a class="contactLink" title="<?php echo get_theme_mod('customTheme-main-callout-telephone') ?>" href="mailto:<?php echo get_theme_mod('customTheme-main-callout-telephone') ?>"><span class="icon"><i class="icon-phone"></i></span><span class="innerText"><?php echo get_theme_mod('customTheme-main-callout-telephone-label') ?></span></a>
              </div>
              <div class="address"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
<div id="pageContainer" class="transition-fade blocks">
