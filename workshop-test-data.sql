-- Test data voor workshop functionaliteit
-- <PERSON><PERSON><PERSON> dit uit in de WordPress database om test workshops aan te maken

-- Voeg test workshops toe
INSERT INTO wp_posts (post_title, post_content, post_status, post_type, post_date, post_date_gmt, post_modified, post_modified_gmt, post_author, comment_status, ping_status) VALUES
('Bodemanalyse Workshop', 'Een uitgebreide workshop over bodemanalyse technieken en interpretatie van resultaten.', 'publish', 'workshop', NOW(), UTC_TIMESTAMP(), NOW(), UTC_TIMESTAMP(), 1, 'closed', 'closed'),
('Duurzame Landbouw Praktijken', 'Leer over duurzame landbouwmethoden en hun impact op bodemkwaliteit.', 'publish', 'workshop', NOW(), UTC_TIMESTAMP(), NOW(), UTC_TIMESTAMP(), 1, 'closed', 'closed'),
('Compost en Organische Stof', 'Workshop over het maken en gebruiken van compost voor bodemverbetering.', 'publish', 'workshop', NOW(), UTC_TIMESTAMP(), NOW(), UTC_TIMESTAMP(), 1, 'closed', 'closed'),
('Bodembiologie Masterclass', 'Diepgaande workshop over bodembiologie en micro-organismen.', 'publish', 'workshop', NOW(), UTC_TIMESTAMP(), NOW(), UTC_TIMESTAMP(), 1, 'closed', 'closed');

-- Haal de post IDs op voor de meta data
SET @workshop1_id = (SELECT ID FROM wp_posts WHERE post_title = 'Bodemanalyse Workshop' AND post_type = 'workshop' LIMIT 1);
SET @workshop2_id = (SELECT ID FROM wp_posts WHERE post_title = 'Duurzame Landbouw Praktijken' AND post_type = 'workshop' LIMIT 1);
SET @workshop3_id = (SELECT ID FROM wp_posts WHERE post_title = 'Compost en Organische Stof' AND post_type = 'workshop' LIMIT 1);
SET @workshop4_id = (SELECT ID FROM wp_posts WHERE post_title = 'Bodembiologie Masterclass' AND post_type = 'workshop' LIMIT 1);

-- Voeg workshop meta data toe (datums en prijzen)
INSERT INTO wp_postmeta (post_id, meta_key, meta_value) VALUES
(@workshop1_id, 'workshop_date', '2024-12-15'),
(@workshop1_id, 'workshop_price', '150'),
(@workshop1_id, 'workshop_duration', '4 uur'),
(@workshop1_id, 'workshop_location', 'Soilmania Hoofdkantoor'),

(@workshop2_id, 'workshop_date', '2024-12-22'),
(@workshop2_id, 'workshop_price', '200'),
(@workshop2_id, 'workshop_duration', '6 uur'),
(@workshop2_id, 'workshop_location', 'Online'),

(@workshop3_id, 'workshop_date', '2025-01-10'),
(@workshop3_id, 'workshop_price', '125'),
(@workshop3_id, 'workshop_duration', '3 uur'),
(@workshop3_id, 'workshop_location', 'Praktijklocatie'),

(@workshop4_id, 'workshop_date', '2025-01-25'),
(@workshop4_id, 'workshop_price', '300'),
(@workshop4_id, 'workshop_duration', '8 uur'),
(@workshop4_id, 'workshop_location', 'Laboratorium');

-- Voeg ACF field group toe voor workshops (optioneel, kan ook via admin worden gedaan)
INSERT INTO wp_posts (post_title, post_content, post_status, post_type, post_date, post_date_gmt, post_modified, post_modified_gmt, post_author, comment_status, ping_status, post_name) VALUES
('Workshop Fields', 'a:4:{s:8:"location";a:1:{i:0;a:1:{i:0;a:3:{s:5:"param";s:9:"post_type";s:8:"operator";s:2:"==";s:5:"value";s:8:"workshop";}}}s:8:"position";s:6:"normal";s:5:"style";s:7:"default";s:15:"label_placement";s:3:"top";}', 'publish', 'acf-field-group', NOW(), UTC_TIMESTAMP(), NOW(), UTC_TIMESTAMP(), 1, 'closed', 'closed', 'group_workshop_fields');

SET @field_group_id = LAST_INSERT_ID();

-- Voeg ACF fields toe
INSERT INTO wp_posts (post_title, post_content, post_status, post_type, post_date, post_date_gmt, post_modified, post_modified_gmt, post_author, comment_status, ping_status, post_name, post_parent, menu_order) VALUES
('Workshop Datum', 'a:16:{s:10:"aria-label";s:0:"";s:4:"type";s:4:"date";s:12:"instructions";s:0:"";s:8:"required";i:1;s:17:"conditional_logic";i:0;s:7:"wrapper";a:3:{s:5:"width";s:0:"";s:5:"class";s:0:"";s:2:"id";s:0:"";}s:13:"display_format";s:5:"d/m/Y";s:13:"return_format";s:5:"Y-m-d";s:9:"first_day";i:1;s:4:"name";s:13:"workshop_date";s:5:"label";s:13:"Workshop Datum";s:11:"placeholder";s:0:"";s:7:"prepend";s:0:"";s:6:"append";s:0:"";s:9:"maxlength";s:0:"";s:8:"readonly";i:0;}', 'publish', 'acf-field', NOW(), UTC_TIMESTAMP(), NOW(), UTC_TIMESTAMP(), 1, 'closed', 'closed', 'field_workshop_date', @field_group_id, 0),

('Workshop Prijs', 'a:13:{s:10:"aria-label";s:0:"";s:4:"type";s:6:"number";s:12:"instructions";s:0:"";s:8:"required";i:0;s:17:"conditional_logic";i:0;s:7:"wrapper";a:3:{s:5:"width";s:0:"";s:5:"class";s:0:"";s:2:"id";s:0:"";}s:13:"default_value";s:0:"";s:11:"placeholder";s:0:"";s:7:"prepend";s:1:"€";s:6:"append";s:0:"";s:3:"min";s:0:"";s:3:"max";s:0:"";s:4:"step";s:0:"";s:4:"name";s:14:"workshop_price";s:5:"label";s:13:"Workshop Prijs";}', 'publish', 'acf-field', NOW(), UTC_TIMESTAMP(), NOW(), UTC_TIMESTAMP(), 1, 'closed', 'closed', 'field_workshop_price', @field_group_id, 1),

('Workshop Duur', 'a:11:{s:10:"aria-label";s:0:"";s:4:"type";s:4:"text";s:12:"instructions";s:0:"";s:8:"required";i:0;s:17:"conditional_logic";i:0;s:7:"wrapper";a:3:{s:5:"width";s:0:"";s:5:"class";s:0:"";s:2:"id";s:0:"";}s:13:"default_value";s:0:"";s:11:"placeholder";s:12:"bijv. 4 uur";s:7:"prepend";s:0:"";s:6:"append";s:0:"";s:9:"maxlength";s:0:"";s:4:"name";s:17:"workshop_duration";s:5:"label";s:13:"Workshop Duur";}', 'publish', 'acf-field', NOW(), UTC_TIMESTAMP(), NOW(), UTC_TIMESTAMP(), 1, 'closed', 'closed', 'field_workshop_duration', @field_group_id, 2),

('Workshop Locatie', 'a:11:{s:10:"aria-label";s:0:"";s:4:"type";s:4:"text";s:12:"instructions";s:0:"";s:8:"required";i:0;s:17:"conditional_logic";i:0;s:7:"wrapper";a:3:{s:5:"width";s:0:"";s:5:"class";s:0:"";s:2:"id";s:0:"";}s:13:"default_value";s:0:"";s:11:"placeholder";s:0:"";s:7:"prepend";s:0:"";s:6:"append";s:0:"";s:9:"maxlength";s:0:"";s:4:"name";s:18:"workshop_location";s:5:"label";s:16:"Workshop Locatie";}', 'publish', 'acf-field', NOW(), UTC_TIMESTAMP(), NOW(), UTC_TIMESTAMP(), 1, 'closed', 'closed', 'field_workshop_location', @field_group_id, 3);
