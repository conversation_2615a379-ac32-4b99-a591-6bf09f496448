<?php
$size = 'medium'; // (thumbnail, medium, large, full or custom size)
$cases = get_posts(array(
    'posts_per_page' => 3,
    'order' => 'ASC',
    'post_type' => 'case-study',
));
$the_query = new WP_Query( $cases );
$backgroundColor = get_field("background_color");
$title = get_field('title');
$pattern = '/(.*)\*(.*)\*(.*)/';
$replacement = '$1<span class="primary">$2</span>$3';
?>

<section class="relatedCasesBlock <?php echo($backgroundColor); ?>" data-init>
  <div class="contentWrapper">
    <div class="cols">
      <div class="col">
        <h2 class="title"><?php echo preg_replace($pattern, $replacement, $title); ?></h2>
      </div>
      <div class="col">
        <a href="<?php the_field("link") ?>" class="button" title="<?php the_field("label") ?>"><?php the_field("label") ?></a>
      </div>
    </div>
    <div class="cases">
      <?php if( $cases ):?>
        <?php foreach( $cases as $post ): setup_postdata($post);
        $image = get_field( 'image', $post->ID );
        $title = get_field( 'title', $post->ID );
        $description = get_field( 'description', $post->ID );
        $date = get_field( 'date', $post->ID );
        $image = get_field( 'image', $post->ID );
        $categories = get_field('category', $post->ID);
        ?>
        <a class="case" href="<?php the_permalink($post->ID); ?>" data-categories="<?php foreach( $categories as $category ): ?><?php echo esc_html( $category->name ); ?>,<?php endforeach; ?>" title="<?php echo esc_html( $title ); ?>">
          <span class="imageWrapper">
            <span class="innerImage">
              <img class="lazy" data-src="<?php echo esc_url($image["sizes"]['large']); ?>" alt="<?php echo esc_attr($image['alt']); ?>" />
            </span>
          </span>
          <span class="normalTitle"><?php echo esc_html( $title ); ?></span>
          <span class="textLink"><?php the_field("label") ?><i class="icon-arrow-right"></i><i class="icon-arrow-right"></i></span>
        </a>
      <?php endforeach; ?>
      <?php wp_reset_postdata(); ?>
      <?php endif; ?>
    </div>
  </div>
</section>
