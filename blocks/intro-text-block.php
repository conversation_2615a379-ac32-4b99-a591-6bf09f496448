<?php $backgroundColor = get_field("background_color");
$pattern = '/(.*)\*(.*)\*(.*)/';
$replacement = '$1<span class="secondary">$2</span>$3';
$intro = get_field("intro");
$youtubeLink = wp_oembed_get(get_field('youtube_embed'));
?>

<section class="introTextBlock <?php echo($backgroundColor); ?>" data-init>
  <div class="contentWrapper">
    <div class="col">
      <h2 class="subTitle secondary"><?php the_field("subtitle") ?></h2>
    </div>
    <div class="col">
      <?php if (get_field("intro")){ ?>
        <div class="normalTitle splitThis" data-init>
          <?php echo preg_replace($pattern, $replacement, $intro); ?>
        </div>
      <?php } ?>
      <div class="text<?php if (get_field("intro")){ ?> delay<?php } ?>">
        <?php the_field("text") ?>
        <?php $link = get_field("link"); if( $link ): ?>
            <?php while( have_rows('link') ): the_row(); ?>
              <?php if (get_sub_field("link") && get_sub_field("label")): ?>
              <a class="textLink" target="_blank" href="<?php the_sub_field("link") ?>" title="<?php the_sub_field("label") ?>"><?php the_sub_field("label") ?><i class="icon-arrow-right"></i><i class="icon-arrow-right"></i></a>
            <?php endif; endwhile; ?>
        <?php endif; ?>
        <?php $urlLink = get_field("url_link"); if( $urlLink ): ?>
            <?php while( have_rows('url_link') ): the_row(); ?>
              <?php if (get_sub_field("link-2") && get_sub_field("label-2")): ?>
              <a class="textLink" target="_blank" href="<?php the_sub_field("link-2") ?>" title="<?php the_sub_field("label-2") ?>"><?php the_sub_field("label-2") ?><i class="icon-arrow-right"></i><i class="icon-arrow-right"></i></a>
            <?php endif; endwhile; ?>
        <?php endif; ?>
      </div>

      <?php $youtubeLink = get_field("youtube_embed"); if ($youtubeLink) { ?>
        <div class="videoWrapper">
        <?php the_field('youtube_embed'); ?>
        </div>
      <?php } ?>
      <?php $spotifyLink = get_field("spotify_embed"); if ($spotifyLink) { ?>
        <iframe src="<?php the_field('spotify_embed') ?>" width="100%" height="380" frameBorder="0" allowfullscreen="" allow="autoplay; clipboard-write; encrypted-media; fullscreen; picture-in-picture"></iframe>
      <?php } ?>
    </div>
  </div>
</section>
