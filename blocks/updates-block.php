<?php
$size = 'medium'; // (thumbnail, medium, large, full or custom size)

$updates = get_posts(array(
    'posts_per_page' => -1,
    'order' => 'ASC',
    'post_type' => 'update',
));
$the_query = new WP_Query( $updates );
$title = get_field('title');
$pattern = '/(.*)\*(.*)\*(.*)/';
$replacement = '$1<span class="primary">$2</span>$3';

$years = array();
$currentCategories = array();
$currentMembers = array();

  if( $updates ):
    foreach( $updates as $post ): setup_postdata($post);
      $date = get_field( 'date', $post->ID );
      $year = date('Y', strtotime($date));
      $categories = get_field('category', $post->ID);
      $relatedMembers = get_field('related-members', $post->ID);

      if( $relatedMembers ) {
        foreach( $relatedMembers as $memberItem ): setup_postdata($memberItem);
          $memberName = get_field( 'name', $memberItem->ID );
          if (!in_array($memberName, $currentMembers, true)) {
            $currentMembers[] = $memberName;
          }
        endforeach;
      }

      if ($categories) {
        foreach( $categories as $category ):
          if (!in_array($category->name, $currentCategories, true)) {
            $currentCategories[] = $category->name;
          }
        endforeach;
      }

      if (!in_array($year, $years, true)) {
        $years[] = $year;
      }
    endforeach;
    wp_reset_postdata();
  endif;
?>

<section class="updatesBlock grey" data-init>
  <div class="imageCursor">
    <div class="innerImage"></div>
  </div>
  <div class="contentWrapper">
    <div class="filters">
      <div class="col">
        <h2 class="normalTitle"><?php the_field("title") ?></h2>
      </div>
      <div class="col">
        <select class="customSelect select2 categoryFilter" data-placeholder="Select category">
          <option value="all"><?php the_field("category_filter_text") ?></option>
          <?php foreach ($currentCategories as $currentCategory) { ?>
            <option value="<?php echo esc_html( $currentCategory ); ?>"><?php echo esc_html( $currentCategory ); ?></option>
          <?php } ?>
        </select>
        <select class="customSelect select2 yearFilter" data-placeholder="Select a year">
          <option value="all"><?php the_field("year_filter_text") ?></option>
          <?php foreach ($years as $yearItem) { ?>
            <option value="<?php echo esc_html( $yearItem ); ?>"><?php echo esc_html( $yearItem ); ?></option>
          <?php } ?>
        </select>
        <select class="customSelect select2 authorFilter" data-placeholder="Select author">
          <option value="all"><?php the_field("author_filter_text") ?></option>
          <?php foreach ($currentMembers as $currentMemberItem) { ?>
            <option value="<?php echo esc_html( $currentMemberItem ); ?>"><?php echo esc_html( $currentMemberItem ); ?></option>
          <?php } ?>
        </select>
      </div>
    </div>
    <div class="updates">
      <?php if( $updates ): ?>
        <?php foreach( $updates as $post ): setup_postdata($post);
        $image = get_field( 'image', $post->ID );
        $title = get_field( 'title', $post->ID );
        $description = get_field( 'description', $post->ID );
        $date = get_field( 'date', $post->ID );
        $year = date('Y', strtotime($date));
        $image = get_field( 'image', $post->ID );
        $categories = get_field('category', $post->ID);
        $relatedMembers = get_field('related-members', $post->ID);
        ?>
        <a data-authors="<?php foreach( $relatedMembers as $memberItem ): setup_postdata($memberItem);$memberName = get_field( 'name', $memberItem->ID ); echo($memberName.','); endforeach; ?>" data-year="<?php echo esc_html( $year ); ?>" <?php if ($categories) { ?>data-categories="<?php foreach( $categories as $category ): ?><?php echo esc_html( $category->name ); ?><?php endforeach; ?>"<?php } ?> data-mouse-image="<?php echo esc_url($image['sizes'][$size]); ?>" href="<?php the_permalink($post->ID); ?>" title="<?php echo esc_html( $title ); ?>" class="update">
          <span class="cols">
            <span class="col">
              <?php if ($categories) { ?>
              <?php foreach( $categories as $category ): ?>
              <span class="category"><?php echo esc_html( $category->name ); ?></span>
              <?php endforeach; ?>
              <?php } ?>
            </span>
            <span class="col"><?php echo esc_html( $date ); ?></span>
            <span class="col"><span class="normalTitle"><?php echo esc_html( $title ); ?></span></span>
            <span class="col">
              <?php
              if( $relatedMembers ) {
                foreach( $relatedMembers as $memberItem ): setup_postdata($memberItem);
                $memberName = get_field( 'name', $memberItem->ID );
                  ?>
                  <span class="innerText"><?php echo($memberName); ?></span>
                  <?php
                endforeach;
              } ?>
            </span>
          </span>
        </a>
      <?php endforeach; ?>
      <?php wp_reset_postdata(); ?>
      <?php endif; ?>
      <div class="noResults" style="display:none;"><?php the_field("no_results_text") ?></div>
    </div>
  </div>
</section>
