<?php
$size = 'medium'; // (thumbnail, medium, large, full or custom size)

$cases = get_posts(array(
    'posts_per_page' => -1,
    'order' => 'ASC',
    'post_type' => 'case-study',
));
$the_query = new WP_Query( $cases );
$title = get_field('title');
$pattern = '/(.*)\*(.*)\*(.*)/';
$replacement = '$1<span class="primary">$2</span>$3';

$currentCategories = array();

if( $cases ):
  foreach( $cases as $post ): setup_postdata($post);
  $categories = get_field('category', $post->ID);
    if ($categories) {
      foreach( $categories as $category ):
        if (!in_array($category->name, $currentCategories, true)) {
          $currentCategories[] = $category->name;
        }
      endforeach;
    }
  endforeach;
  wp_reset_postdata();
  endif;
?>

<section class="casesBlock" data-init>
  <div class="contentWrapper">
    <div class="cases">
      <div class="filters">
        <h2 class="subTitle"><?php the_field("title") ?></h2>
        <?php foreach ($currentCategories as $currentCategory) { ?>
          <span class="filter">
            <input type="checkbox" checked id="<?php echo esc_html( $currentCategory ); ?>" name="<?php echo esc_html( $currentCategory ); ?>" value="<?php echo esc_html( $currentCategory ); ?>">
            <span class="checkMark"></span>
            <label for="<?php echo esc_html( $currentCategory ); ?>"><?php echo esc_html( $currentCategory ); ?></label><br>
          </span>
        <?php } ?>
      </div>
      <?php if( $cases ):?>
        <?php foreach( $cases as $post ): setup_postdata($post);
        $image = get_field( 'image', $post->ID );
        $title = get_field( 'title', $post->ID );
        $description = get_field( 'description', $post->ID );
        $date = get_field( 'date', $post->ID );
        $image = get_field( 'image', $post->ID );
        $categories = get_field('category', $post->ID);
        ?>
        <a class="case" href="<?php the_permalink($post->ID); ?>" data-categories="<?php foreach( $categories as $category ): ?><?php echo esc_html( $category->name ); ?>,<?php endforeach; ?>" title="<?php echo esc_html( $title ); ?>">
          <span class="imageWrapper">
            <span class="innerImage">
              <img class="lazy" data-src="<?php echo esc_url($image["sizes"]['large']); ?>" alt="<?php echo esc_attr($image['alt']); ?>" />
            </span>
          </span>
          <span class="normalTitle"><?php echo esc_html( $title ); ?></span>
          <span class="textLink"><?php the_field("label") ?><i class="icon-arrow-right"></i><i class="icon-arrow-right"></i></span>
        </a>
      <?php endforeach; ?>
      <?php wp_reset_postdata(); ?>
      <?php endif; ?>
      <div class="noResults" style="display:none;"><?php the_field("no_results_text") ?></div>
    </div>
  </div>
</section>
