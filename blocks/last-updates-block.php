<?php
$size = 'medium'; // (thumbnail, medium, large, full or custom size)

$updates = get_posts(array(
    'posts_per_page' => '3',
    'order' => 'ASC',
    'post_type'     => 'update',
));
$the_query = new WP_Query( $updates );
$title = get_field('title');
$pattern = '/(.*)\*(.*)\*(.*)/';
$replacement = '$1<span class="primary">$2</span>$3';
?>

<section class="lastUpdatesBlock grey" data-init>
  <div class="imageCursor">
    <div class="innerImage"></div>
  </div>
  <div class="contentWrapper">
    <h2 class="title splitThis" data-init><?php echo preg_replace($pattern, $replacement, $title); ?></h2>
    <div class="updates">
      <?php if( $updates ): ?>
        <?php foreach( $updates as $post ): setup_postdata($post);
        $image = get_field( 'image', $post->ID );
        $title = get_field( 'title', $post->ID );
        $description = get_field( 'description', $post->ID );
        $date = get_field( 'date', $post->ID );
        $image = get_field( 'image', $post->ID );
        $categories = get_field('category', $post->ID);
        ?>
        <a data-mouse-image="<?php echo esc_url($image['sizes'][$size]); ?>" href="<?php the_permalink($post->ID); ?>" title="<?php echo esc_html( $title ); ?>" class="update">
          <span class="cols">
            <span class="col">
              <?php if ($categories) { ?>
              <?php foreach( $categories as $category ): ?>
              <span class="category"><?php echo esc_html( $category->name ); ?></span>
              <?php endforeach; ?>
              <?php } ?>
            </span>
            <span class="col"><?php echo esc_html( $date ); ?></span>
            <span class="col"><span class="normalTitle"><?php echo esc_html( $title ); ?></span></span>
          </span>
        </a>
      <?php endforeach; ?>
      <?php wp_reset_postdata(); ?>
      <?php endif; ?>
    </div>
  </div>
</section>
