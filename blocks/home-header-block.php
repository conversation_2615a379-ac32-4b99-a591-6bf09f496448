<?php $image = get_field('background_image');
 $video = get_field('background_video');
$size = 'full'; // (thumbnail, medium, large, full or custom size)

?>

<section class="homeHeaderBlock" data-init>
  <div class="background">
    <?php if ($video): ?>
    <video poster="<?php echo esc_url($image['url']); ?>" class="video" muted playsinline loop autoplay>
        <source src="<?php echo esc_url($video); ?>" type="video/mp4">
    </video>
    <?php elseif( $image ): ?>
      <img src="<?php echo esc_url($image['url']); ?>" alt="<?php echo esc_attr($image['alt']); ?>" />
    <?php endif; ?>
  </div>
  <div class="contentWrapper">
    <h1 class="bigTitle splitThis" data-init><?php the_field("title") ?></h1>
    <div class="innerContent">
      <div class="cols">
        <div class="col">
          <div class="textButtonWrapper">
            <div class="text white"><?php the_field("text") ?></div>
            <div class="buttons">
              <?php if( have_rows('button') ): ?>
                <?php while( have_rows('button') ): the_row(); ?>
                    <a class="button <?php the_sub_field("type") ?>" href="<?php the_sub_field("link") ?>" title="<?php the_sub_field("label") ?>"><?php the_sub_field("label") ?></a>
                <?php endwhile; ?>
              <?php endif; ?>
            </div>
          </div>
        </div>
        <div class="col right">
          <div class="links">
            <?php if( have_rows('link_item') ): ?>
              <h2 class="subTitle"><?php the_field("links_subtitle") ?></h2>
              <?php while( have_rows('link_item') ): the_row(); ?>
                  <a class="pageLink" href="<?php the_sub_field("link") ?>" title="<?php the_sub_field("label") ?>"><?php the_sub_field("label") ?><i class="icon-arrow-right"></i><i class="icon-arrow-right"></i></a>
              <?php endwhile; ?>
            <?php endif; ?>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
