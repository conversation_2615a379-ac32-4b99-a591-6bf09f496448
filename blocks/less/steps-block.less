.stepsBlock {
  .contentWrapper {
    svg {
      display: inline-block;
      width: @vw60;
      height: auto;
      object-fit: contain;
    }
    .title {
      margin-top: @vw20;
    }
    .steps {
      margin-top: @vw80;
      margin-left: -@vw16 / 2;
      width: calc(100% ~"+" @vw16);
      .step {
        display: inline-block;
        vertical-align: top;
        margin: 0 @vw16 / 2;
        width: calc(33.3333% ~"-" @vw16);
        .normalTitle {
          margin: @vw10 0 @vw20 0;
        }
        i {
          color: @secondaryColor;
          font-size: @vw22;
        }
        .text {
          opacity: .7;
          padding-right: @vw40;
        }
      }
    }
  }
}

@media all and (max-width: 1160px) {
  .stepsBlock {
    .contentWrapper {
      svg {
        width: @vw60-1160;
      }
      .title {
        margin-top: @vw20-1160;
      }
      .steps {
        margin-top: @vw80-1160;
        margin-left: -@vw16-1160 / 2;
        width: calc(100% ~"+" @vw16-1160);
        .step {
          width: calc(33.3333% ~"-" @vw16-1160);
          .normalTitle {
            margin: @vw10-1160 0 @vw20-1160 0;
          }
          i {
            font-size: @vw22-1160;
          }
          .text {
            padding-right: @vw40-1160;
          }
        }
      }
    }
  }
}

@media all and (max-width: 580px) {
  .stepsBlock {
    .contentWrapper {
      svg {
        width: @vw60-580;
      }
      .title {
        margin-top: @vw20-580;
      }
      .steps {
        margin-top: @vw80-580;
        margin-left: -@vw16-580 / 2;
        width: calc(100% ~"+" @vw16-580);
        .step {
          width: 100%;
          .normalTitle {
            margin: @vw10-580 0 @vw20-580 0;
          }
          i {
            font-size: @vw22-580;
          }
          .text {
            padding-right: @vw40-580;
          }
        }
      }
    }
  }
}
