.servicesBlock {
  .services {
    margin-top: @vw40 + @vw40;
    .service {
      cursor: pointer;
      position: relative;
      display: block;
      padding: @vw40 0 @vw40 0;
      text-decoration: none;
      color: @primaryColor;
      .transition(.3s);
      &:hover {
        color: @secondaryColor;
        .col {
          .imageWrapper {
            img {
              transform: translate3d(0,0,0) scale(1.1);
            }
          }
        }
        .normalTitle {
          padding-left: @vw20;
          padding-right: 0;
          transition-delay: .15s;
        }
        &:before {
          width: 0% !important;
          .transitionMore(width, .3s, .15s, ease-in-out);
        }
        &:after {
          width: 100%;
          transition-delay: .15s;
        }
      }
      &:before, &:after {
        content: '';
        top: auto;
        bottom: 0;
        position: absolute;
        right: 0;
        height: 1px;
        background: @primaryColor;
        transition-delay: .15s;
        transition: width 0.6s cubic-bezier(0.83, 0, 0.17, 1);
        width: 100%;
      }
      &:after {
        right: auto;
        left: 0;
        background: @thirdColor;
        width: 0%;
        transition-delay: 0;
      }
      * {
        cursor: pointer;
      }
      .cols {
        display: block;
        width: 100%;
      }
      .col {
        display: inline-block;
        vertical-align: top;
        width: 33.1%;
        &:nth-child(1), &:nth-child(2) {
          padding-right: @vw112;
        }
        .imageWrapper {
          border-radius: 0 @vw50 0 @vw50;
          overflow: hidden;
          position: relative;
          height: @vw100 + @vw80;
          margin: auto;
          display: block;
          vertical-align: top;
          max-width: 100%;
          width: (@vw112 * 3) + (@vw16 * 2);
          img {
            position: absolute;
            height: 100%;
            width: 100%;
            left: 0;
            top: 0;
            object-fit: cover;
            object-position: center;
            transition: transform .3s cubic-bezier(0.68, -0.6, 0.32, 1.6);
            transform: translate3d(0,0,0) scale(1);
            mix-blend-mode: luminosity;
          }
          &:after {
            content: '';
            position: absolute;
            height: 100%;
            width: 100%;
            left: 0;
            top: 0;
            background: @secondaryColor;
            opacity: .4;
            transform: translate3d(0,0,0);
          }
        }
        .normalTitle {
          height: @vw44 * 3;
          overflow: hidden;
          width: 100%;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          .transition(.3s);
        }
        .description {
          display: block;
          display: -webkit-box;
          height: @vw22 * 3;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }
}

@media all and (max-width: 1160px) {
  .servicesBlock {
    .services {
      margin-top: @vw40-1160 + @vw40-1160;
      .service {
        .cols {
          white-space: nowrap;
          .col {
            white-space: normal;
            &:nth-child(1), &:nth-child(2) {
              padding-right: @vw32-1160;
            }
          }
        }
        .col {
          .imageWrapper {
            width: (@vw112-1160 * 2) + (@vw16-1160);
            height: @vw100-1160 + @vw80-1160;
          }
          .normalTitle {
            height: @vw44-1160 * 3;
          }
          .description {
            height: @vw22-1160 * 3;
          }
        }
      }
    }
  }
}

@media all and (max-width: 580px) {
  .servicesBlock {
    .services {
      margin-top: @vw40-580 + @vw40-580;
      .service {
        .cols {
          .col {
            width: 50%;
            &:first-child {
              display: none;
            }
            &:nth-child(1), &:nth-child(2) {
              padding-right: 0;
            }
          }
        }
        .col {
          width: 100%;
          .imageWrapper {
            width: 100%;
            height: @vw100-580 + @vw80-580;
          }
          .normalTitle {
            height: @vw44-580 * 3;
          }
          .description {
            height: @vw22-580 * 3;
          }
        }
      }
    }
  }
}
