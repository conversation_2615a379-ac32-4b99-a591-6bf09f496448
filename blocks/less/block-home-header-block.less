.homeHeaderBlock {
  position: relative;
  &.inview {
    .contentWrapper {
      .innerContent {
        .textButtonWrapper {
          .text, .buttons {
            opacity: 1;
            transform: translateY(0);
            .transition(.45s);
          }
          .text {
            transition-delay: .6s;
          }
          .buttons {
            transition-delay: .9s;
          }
        }
        .links {
          opacity: 1;
          transform: translateY(0);
          .transition(.45s);
          transition-delay: 1.05s;
        }
      }
    }
  }
  &:first-child {
    padding-top: 0;
    margin-top: @vw100 + @vw20;
  }
  .background {
    border-radius: @vw100 0 @vw100 0;
    position: absolute;
    height: 100%;
    width: calc(100vw ~"-" @vw16 ~"-" @vw16);
    left: @vw16;
    overflow: hidden;
    &:after {
      content: '';
      background: @almostBlack;
      opacity: .4;
      position: absolute;
      top: 0;
      left: 0;
      height: 100%;
      width: 100%;
    }
    img, video {
      position: absolute;
      top: 0;
      left: 0;
      object-fit: cover;
      height: 100%;
      width: 100%;
    }
  }
  .contentWrapper {
    padding-top: @vw100;
    padding-bottom: @vw100;
    .bigTitle {
      max-width: 100%;
      width: (@vw112 * 7) + (@vw16 * 7);
    }
    .innerContent {
      margin-top: @vw90;
      .textButtonWrapper {
        max-width: 100%;
        width: 100%;
        // width: (@vw112 * 4) + (@vw16 * 3);
        .text, .buttons {
          opacity: 0;
          transform: translateY(@vw20);
        }
        .buttons {
          margin-top: @vw50;
          .button {
            margin-right: @vw20;
            &:last-child {
              margin-right: 0;
            }
          }
        }
      }
      .subTitle {
        color: @hardWhite;
        opacity: .4;
      }
      .links {
        display: inline-block;
        text-align: left;
        position: relative;
        max-width: 100%;
        width: (@vw112 * 3) + (@vw16 * 2);
        opacity: 0;
        transform: translateY(@vw20);
        .pageLink {
          color: @hardWhite;
          padding: @vw30 0;
          display: block;
          position: relative;
          text-decoration: none;
          padding-right: @vw40;
          cursor: pointer;
          width: 100%;
          .transition(.3s);
          &:hover {
            padding-left: @vw40;
            padding-right: 0;
            &:after {
              width: 0;
            }
            i {
              &:first-child {
                left: 0;
                opacity: 1;
                transition-delay: .15s;
              }
              &:last-child {
                right: -@vw10;
                opacity: 0;
                transition-delay: 0s;
              }
            }
          }
          &:after {
            content: '';
            width: 100%;
            position: absolute;
            top: auto;
            right: 0;
            .transition(.15s);
            bottom: 0;
            opacity: .4;
            height: 1px;
            background: @hardWhite;
          }
          i {
            cursor: pointer;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            .transition(.3s);
            &:first-child {
              left: -@vw10;
              opacity: 0;
            }
            &:last-child {
              right: 0;
              transition-delay: .15s;
            }
          }
        }
      }
      .col {
        width: 50%;
        vertical-align: middle;
        display: inline-block;
        &:first-child {
          padding-left: @vw112 + @vw16;
        }
        &.right {
          text-align: right;
        }
      }
    }
  }
}

@media all and (max-width: 1160px) {
  .homeHeaderBlock {
    &:first-child {
      margin-top: @vw100-1160;
    }
    .background {
      border-radius: @vw100-1160 0 @vw100-1160 0;
      width: calc(100vw ~"-" @vw16-1160 ~"-" @vw16-1160);
      left: @vw16-1160;
    }
    .contentWrapper {
      padding-top: @vw100-1160;
      padding-bottom: @vw100-1160;
      .bigTitle {
        width: (@vw112-1160 * 7) + (@vw16-1160 * 7);
      }
      .innerContent {
        margin-top: @vw90-1160;
        .textButtonWrapper {
          // width: (@vw112-1160 * 4) + (@vw16-1160 * 3);
          .text, .buttons {
            transform: translateY(@vw20-1160);
          }
          .buttons {
            margin-top: @vw50-1160;
            .button {
              margin-right: @vw20-1160;
            }
          }
        }
        .links {
          width: (@vw112-1160 * 3) + (@vw16-1160 * 2);
          transform: translateY(@vw20-1160);
          .pageLink {
            padding: @vw30-1160 0;
            padding-right: @vw40-1160;
            &:hover {
              padding-left: @vw40-1160;
            }
            &:after {
              width: 100%;
              opacity: .4;
              background: @hardWhite;
            }
            i {
              &:first-child {
                left: -@vw10-1160;
              }
              &:last-child {
                right: 0;
              }
            }
          }
        }
        .col {
          &:first-child {
            padding-left: 0;
          }
        }
      }
    }
  }
}

@media all and (max-width: 580px) {
  .homeHeaderBlock {
    &:first-child {
      margin-top: @vw100-580;
    }
    .background {
      border-radius: @vw50-580 0 @vw50-580 0;
      width: calc(100vw ~"-" @vw16-580 ~"-" @vw16-580);
      left: @vw16-580;
      &:after {
        opacity: .7;
      }
    }
    .contentWrapper {
      padding: @vw50-580;
      padding-top: @vw100-580 + @vw32-580;
      padding-bottom: @vw100-580;
      .bigTitle {
        width: (@vw112-580 * 7) + (@vw16-580 * 7);
      }
      .innerContent {
        margin-top: @vw50-580;
        .col {
          width: 100%;
        }
        .textButtonWrapper {
          width: 100%;
          .text, .buttons {
            transform: translateY(@vw20-580);
          }
          .buttons {
            margin-top: @vw50-580;
            .button {
              margin-right: 0;
              text-align: center;
              width: 100%;
              &:first-child {
                margin-bottom: @vw20-580;
              }
            }
          }
        }
        .links {
          margin-top: @vw50-580;
          width: 100%;
          transform: translateY(@vw20-580);
          .pageLink {
            padding: @vw20-580 0;
            padding-right: @vw40-580;
            &:hover {
              padding-left: @vw40-580;
            }
            &:after {
              width: 100%;
              opacity: .4;
              background: @hardWhite;
            }
            i {
              &:first-child {
                left: -@vw10-580;
              }
              &:last-child {
                right: 0;
              }
            }
          }
        }
        .col {
          &:first-child {
            padding-left: 0;
          }
        }
      }
    }
  }
}
