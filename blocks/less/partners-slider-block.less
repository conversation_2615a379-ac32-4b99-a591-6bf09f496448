.partnersSliderBlock {
  &.inview {
    .slider {
        opacity: 1;
        transition-delay: .15s;
        transform: translateY(0);
        &.right {
          transition-delay: .45s;
        }
    }
  }
  .slider {
    left: 0;
    top: 0;
    white-space: nowrap;
    width: 100%;
    margin-bottom: @vw20;
    opacity: 0;
    .transition(.3s);
    transform: translateY(@vw20);
    &:last-child {
      margin-bottom: 0;
    }
    &.right {
      .animItem {
        &.animate {
          animation: 80s animateToRight infinite linear;
        }
      }
    }
    .innerSlider {
      position: relative;
    }
    .animItem {
      display: inline-block;
      vertical-align: middle;
      &.animate {
        animation: 80s animateToLeft infinite linear;
      }
    }
    .item {
      background: rgba(255,255,255,1);
      position: relative;
      margin-bottom: @vw32;
      white-space: normal;
      display: inline-block;
      margin: 0 @vw30;
      height: @vw32 + @vw16; 
      width: @vw100 * 2;
      vertical-align: middle;
      overflow: hidden;
      .innerWrapper {
        height: 0;
        padding-bottom: 100%;
        width: 100%;
      }
      img {
        display: block;
        margin: auto;
        height: 100%;
        position: absolute;
        top: 50%;
        object-fit: contain;
        left: 50%;
        transform: translate(-50%, -50%);
        max-width: 90%;
      }
    }
  }
}

@keyframes animateToRight {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(0%);
  }
}

@keyframes animateToLeft {
  0% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(-100%);
  }
}

@media all and (max-width: 1160px) {
  .partnersSliderBlock {
    .slider {
      margin-bottom: @vw20-1160;
      .item {
        padding: 0;
        height: @vw40-1160;
        width: @vw100-1160 * 2;
        margin: 0 @vw30-1160;
      }
    }
  }
}

@media all and (max-width: 580px) {
  .partnersSliderBlock {
    .slider {
      margin-bottom: @vw32-580;
      .item {
        height: @vw40-580;
        width: @vw100-580 * 2;
        margin: 0 @vw30-580;
      }
    }
  }
}
