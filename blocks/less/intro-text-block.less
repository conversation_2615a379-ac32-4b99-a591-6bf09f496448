.introTextBlock {
  &.inview {
    .col {
      .subTitle, .text {
        .transition(.3s);
        opacity: 1;
        transform: translateY(0);
      }
      .text {
        transition-delay: .15s;
        &.delay {
          transition-delay: .9s;
        }
      }
    }
  }
  .col {
    display: inline-block;
    vertical-align: top;
    &:first-child {
      padding-right: @vw112 + (@vw16 * 2);
      width: (@vw112 * 4) + (@vw16 * 4);
    }
    &:last-child {
      width: (@vw112 * 8) + (@vw16 * 7);
    }
    .normalTitle {
      margin-bottom: @vw80;
    }
    .subTitle, .text {
      opacity: 0;
      transform: translateY(@vw20);
    }
    .text {
      padding-right: (@vw112 * 3) + (@vw16 * 3);
    }
    .videoWrapper {
      height: 0;
      margin-top: @vw40;
      padding-bottom: 56.24999999999999%;
      width: 100%;
      position: relative;
      .wp-video, video, .mejs-layers, .mejs-inner, .mejs-container, .mejs-mediaelement * {
        height: 100% !important;
        position: absolute;
        top: 0;
        left: 0;
        width: 100% !important;
      }
      .mejs-container, .mejs-container .mejs-controls, .mejs-embed, .mejs-embed body {
        background: @primaryColor;
      }
      video, iframe {
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        margin-top: 0;
        max-height: 100%;
      }
    }
    iframe {
      margin-top: @vw40;
      max-height: @vw100;
      width: 100%;
    }
  }
}

@media all and (max-width: 1160px) {
  .introTextBlock {
    .contentWrapper {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
    }
    .col {
      &:first-child {
        padding-right: @vw32-1160;
        width: (@vw112-1160 * 2) + (@vw16-1160 * 2);
      }
      &:last-child {
        width: (@vw112-1160 * 5) + (@vw16-1160 * 6);
      }
      .normalTitle {
        margin-bottom: @vw80-1160;
      }
      .subTitle, .text {
        opacity: 0;
        transform: translateY(@vw20-1160);
      }
      .text {
        padding-right: 0;
      }
      .videoWrapper {
        margin-top: @vw40-1160;
        padding-bottom: 56.24999999999999%;
        iframe {
          margin-top: @vw40-1160;
        }
      }
    }
  }
}

@media all and (max-width: 580px) {
  .introTextBlock {
    .col {
      &:first-child {
        padding-right: 0;
        margin-bottom: @vw22-580;
        width: 100%;
      }
      &:last-child {
        width: 100%;
      }
      .normalTitle {
        margin-bottom: @vw80-580;
      }
      .subTitle, .text {
        opacity: 0;
        transform: translateY(@vw20-580);
      }
      .text {
        padding-right: 0;
      }
      .videoWrapper {
        margin-top: @vw40-580;
        padding-bottom: 56.24999999999999%;
        iframe {
          margin-top: @vw40-580;
        }
      }
    }
  }
}
