.teamBlock {
  .col {
    display: inline-block;
    vertical-align: middle;
    &:first-child {
      width: 75%;
    }
    &:last-child {
      width: 25%;
      text-align: right;
    }
  }
  .members {
    margin-left: -@vw16 / 2;
    margin-top: @vw80;
    margin-bottom: -(@vw100 + @vw20);
    width: calc(100% ~"+" @vw16);
    .member {
      display: inline-block;
      vertical-align: top;
      margin: 0 @vw16 / 2;
      margin-bottom: @vw100 + @vw20;
      width: calc(33.3333% ~"-" @vw16);
      .imageWrapper {
        position: relative;
        display: block;
        height: auto;
        margin-bottom: @vw20;
        width: 100%;
        .innerImage {
          position: relative;
          height: 0;
          padding-bottom: 100%;
          width: 100%;
          position: relative;
          img {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            width: 100%;
            object-fit: cover;
            object-position: bottom;
          }
        }
      }
      .titleWrapper {
        .normalTitle {
          height: @vw44;
          overflow: hidden;
          position: relative;
          display: inline-block;
          vertical-align: middle;
          width: calc(100% ~"-" @vw112);
          text-overflow: ellipsis;
        }
        .socials {
          display: inline-block;
          width: @vw112;
          vertical-align: middle;
          text-align: right;
          .social {
            color: @primaryColor;
            text-decoration: none;
            display: inline-block;
            .transition(.3s);
            &:not(last-child) {
              margin-right: @vw10;
            }
            &:hover {
              opacity: .4;
            }
          }
        }
      }
      .textLink {
        margin-top: @vw20;
      }
    }
  }
}

@media all and (max-width: 1160px) {
  .teamBlock {
    .col {
      &:first-child {
        width: 60%;
      }
      &:last-child {
        width: 40%;
      }
    }
    .members {
      margin-left: -@vw16-1160 / 2;
      margin-top: @vw80-1160;
      margin-bottom: -@vw70-1160;
      width: calc(100% + @vw16-1160);
      .member {
        margin: 0 @vw16-1160 / 2;
        margin-bottom: @vw70-1160;
        width: calc(33.3333% ~"-" @vw16-1160);
        .titleWrapper {
          .normalTitle {
            height: @vw32-1160;
            width: calc(100% ~"-" @vw112-1160);
          }
          .socials {
            width: @vw112-1160;
          }
        }
      }
    }
  }
}

@media all and (max-width: 580px) {
  .teamBlock {
    .col {
      &:first-child {
        width: 60%;
      }
      &:last-child {
        width: 40%;
      }
    }
    .members {
      margin-left: -@vw16-580 / 2;
      margin-top: @vw80-580;
      margin-bottom: -@vw70-580;
      width: calc(100% + @vw16-580);
      .member {
        margin: 0 @vw16-580 / 2;
        margin-bottom: @vw70-580;
        width: calc(50% ~"-" @vw16-580);
        .titleWrapper {
          margin-bottom: @vw10-580;
          .normalTitle {
            height: @vw32-580;
            width: calc(100% ~"-" @vw112-580);
          }
          .socials {
            width: @vw112-580;
          }
        }
      }
    }
  }
}
