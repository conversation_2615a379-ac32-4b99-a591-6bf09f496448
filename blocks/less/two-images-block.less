.twoImagesBlock {
  .col {
    display: inline-block;
    vertical-align: top;
    &:first-child {
      padding-right: @vw112 + (@vw16 * 2);
      width: (@vw112 * 4) + (@vw16 * 4);
    }
    &:last-child {
      width: (@vw112 * 8) + (@vw16 * 7);
    }
    img {
      display: block;
      width: 100%;
      height: auto;
      object-fit: contain;
    }
    .text {
      margin-top: @vw10;
    }
  }
}

@media all and (max-width: 1160px) {
  .twoImagesBlock {
    .contentWrapper {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
    }
    .col {
      &:first-child {
        padding-right: @vw32-1160;
        width: (@vw112-1160 * 2) + (@vw16-1160 * 2);
      }
      &:last-child {
        width: (@vw112-1160 * 5) + (@vw16-1160 * 6);
      }
    }
  }
}

@media all and (max-width: 580px) {
  .twoImagesBlock {
    .col {
      &:first-child {
        padding-right: @vw32-580;
        width: 100%;
        margin-bottom: @vw32-580;
      }
      &:last-child {
        padding-left: @vw32-580;
        width: 100%;
      }
    }
  }
}
