.imageTextBlock {

  .col {
    width: 50%;
    vertical-align: top;
    display: inline-block;
    &.text {
      margin-top: @vw80;
      padding-right: @vw112 + @vw16;
    }
  }
  .normalTitle {
    margin-bottom: @vw30;
  }
  .imageWrapper {
    border-radius: 0 @vw100 0 @vw100;
    overflow: hidden;
    position: relative;
    height: auto;
    display: block;
    vertical-align: top;
    max-width: 100%;
    width: (@vw112 * 4) + (@vw16 * 3);
    img {
      position: relative;
      height: auto;
      width: 100%;
      object-fit: contain;
    }
  }
}

@media all and (max-width: 1160px) {
  .imageTextBlock {
    .col {
      width: 50%;
      &.text {
        margin-top: @vw80-1160;
        padding-left: @vw32-1160 + @vw16-1160;
        padding-right: 0;
      }
    }
    .normalTitle {
      margin-bottom: @vw30-1160;
    }
    .imageWrapper {
      width: (@vw112-1160 * 4) + (@vw16-1160 * 3);
      img {
        object-fit: contain;
      }
    }
  }
}

@media all and (max-width: 580px) {
  .imageTextBlock {
    .col {
      width: 100%;
      &.text {
        margin-top: @vw50-580;
        padding-right: 0;
        padding-left: 0;
      }
    }
    .normalTitle {
      margin-bottom: @vw30-580;
    }
    .imageWrapper {
      width: 100%;
      transform: translate3d(0,0,0) !important;
      img {
        object-fit: contain;
      }
    }
  }
}
