.aboutHeaderBlock {
  .subTitle {
    margin-bottom: @vw20;
  }
  .textWrapper {
    display: inline-block;
    vertical-align: top;
    width: (@vw112 * 7) + (@vw16 * 7);
    .text {
      margin-top: @vw60;
      padding: 0 @vw112 + @vw16 0 (@vw112 * 2) + (@vw16 * 2)
    }
  }
  .imageWrapper {
    border-radius: 0 @vw100 0 @vw100;
    overflow: hidden;
    position: relative;
    height: auto;
    display: inline-block;
    vertical-align: top;
    width: (@vw112 * 5) + (@vw16 * 4);
    .innerImage {
      position: relative;
      height: 0;
      padding-bottom: 130.70652173913044%;
      width: 100%;
      position: relative;
      .animateImage, img {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        width: 100%;
      }
      img {
        object-fit: cover;
        object-position: bottom;
      }
    }
  }
}

@media all and (max-width: 1160px) {
  .aboutHeaderBlock {
    .contentWrapper {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      align-items: center;
    }
    .subTitle {
      margin-bottom: @vw20-1160;
    }
    .textWrapper {
      width: (@vw112-1160 * 4) + (@vw16-1160 * 5);
      .text {
        margin-top: @vw60-1160;
        padding: 0;
      }
    }
    .imageWrapper {
      border-radius: 0 @vw100-1160 0 @vw100-1160;
      width: (@vw112-1160 * 3) + (@vw16-1160 * 3);
    }
  }
}

@media all and (max-width: 580px) {
  .aboutHeaderBlock {
    .subTitle {
      margin-bottom: @vw20-580;
    }
    .textWrapper {
      width: 100%;
      .text {
        margin-top: @vw60-580;
        padding: 0;
      }
    }
    .imageWrapper {
      margin-top: @vw32-580;
      border-radius: 0 @vw100-580 0 @vw100-580;
      width: 100%;
      .innerImage {
        padding-bottom: 100%;
      }
    }
  }
}
