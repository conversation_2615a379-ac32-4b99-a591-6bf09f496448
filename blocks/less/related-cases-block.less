.relatedCasesBlock {
  .col {
    display: inline-block;
    vertical-align: middle;
    &:first-child {
      width: 75%;
    }
    &:last-child {
      width: 25%;
      text-align: right;
      .button {
        text-align: center;
      }
    }
  }
  .cases {
    margin-top: @vw80;
    margin-left: -@vw16 / 2;
    width: calc(100% ~"+" @vw16);
    .case {
      color: @primaryColor;
      text-decoration: none;
      display: inline-block;
      vertical-align: top;
      margin: 0 @vw16 / 2;
      width: calc(33.1333% ~"-" @vw16);
      .transition(.3s);
      &:hover {
        color: @thirdColor;
        .imageWrapper {
          .innerImage {
            img {
              transform: translate3d(0,0,0) scale(1.1);
              transition: transform .3s cubic-bezier(0.68, -0.6, 0.32, 1.6);
            }
          }
        }
        .textLink {
          color: @thirdColor;
          padding-left: @vw30;
          padding-right: 0;
          &:before {
            width: 100%;
            background: @thirdColor;
            transition-delay: .15s;
          }
          &:after {
            width: 0;
            background: @thirdColor;
          }
          i {
            color: @thirdColor;
            &:first-child {
              left: 0;
              opacity: 1;
              transition-delay: .15s;
            }
            &:last-child {
              right: -@vw10;
              opacity: 0;
              transition-delay: 0s;
            }
          }
        }
      }
      .imageWrapper {
        overflow: hidden;
        position: relative;
        height: auto;
        display: block;
        vertical-align: top;
        width: 100%;
        .innerImage {
          display: block;
          position: relative;
          height: 0;
          padding-bottom: 56.25%;
          width: 100%;
          position: relative;
          img {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            width: 100%;
            object-fit: cover;
            object-position: center;
            .transition(.3s);
            transform: translate3d(0,0,0) scale(1.05);
          }
        }
      }
      .normalTitle {
        margin: @vw20 0;
        display: block;
        display: -webkit-box;
        height: @vw44 * 3;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    .noResults {
      background: @backgroundGrey;
      padding: @vw20;
    }
  }
}

@media all and (max-width: 1160px) {
  .relatedCasesBlock {
    .col {
      &:first-child {
        width: 70%; // Adjusted width for smaller screens
      }
      &:last-child {
        width: 30%; // Adjusted width for smaller screens
        text-align: right;
      }
    }
    .cases {
      margin: 0 @vw16-1160 / 2;
      margin-top: @vw80-1160;
      margin-left: -@vw16-1160 / 2;
      width: calc(100% ~"+" @vw16-1160);
      .case {
        width: calc(33.1333% ~"-" @vw16-1160);
        .imageWrapper {
          .innerImage {
            img {
              transform: translate3d(0,0,0) scale(1.05); // Adjusted scale for smaller screens
            }
          }
        }
        .normalTitle {
          margin: @vw20-1160 0;
          height: @vw32-1160 * 3;
        }
      }
    }
  }
}

@media all and (max-width: 580px) {
  .relatedCasesBlock {
    .col {
      &:first-child {
        width: 60%; // Adjusted width for very small screens
      }
      &:last-child {
        width: 40%; // Adjusted width for very small screens
        text-align: right;
      }
    }
    .cases {
      margin: 0 @vw16-580 / 2;
      margin-top: @vw80-580;
      margin-left: -@vw16-580 / 2;
      width: calc(100% ~"+" @vw16-580);
      .case {
        width: calc(50% ~"-" @vw16-580);
        &:last-child {
          display: none;
        }
        .imageWrapper {
          .innerImage {
            img {
              transform: translate3d(0,0,0) scale(1.03); // Adjusted scale for very small screens
            }
          }
        }
        .normalTitle {
          margin: @vw20-580 0;
          height: @vw32-580 * 3;
        }
      }
    }
  }
}
