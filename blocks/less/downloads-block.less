.downloadsBlock {
  .col {
    display: inline-block;
    vertical-align: top;
    &:first-child {
      padding-right: @vw112 + (@vw16 * 2);
      width: (@vw112 * 4) + (@vw16 * 4);
    }
    &:last-child {
      width: (@vw112 * 8) + (@vw16 * 7);
    }
    .normalTitle {
      margin-bottom: @vw80;
    }
    .text {
      padding-right: (@vw112 * 3) + (@vw16 * 3);
      margin-bottom: @vw80;
    }
    .downloads {
      padding-right: (@vw112 * 3) + (@vw16 * 3);
      .download {
        margin-bottom: @vw10;
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

@media all and (max-width: 1160px) {
  .downloadsBlock {
    .col {
      &:first-child {
        padding-right: @vw112-1160 + (@vw16-1160 * 2);
        width: (@vw112-1160 * 2) + (@vw16-1160 * 2);
      }
      &:last-child {
        width: (@vw112-1160 * 5) + (@vw16-1160 * 6);
      }
      .normalTitle {
        margin-bottom: @vw80-1160;
      }
      .text {
        padding-right: 0;
        margin-bottom: @vw80-1160;
      }
      .downloads {
        padding-right: 0;
        .download {
          margin-bottom: @vw10-1160;
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }
}

@media all and (max-width: 580px) {
  .downloadsBlock {
    .col {
      &:first-child {
        padding-right: 0;
        width: 100%;
      }
      &:last-child {
        width: 100%;
      }
      .normalTitle {
        margin-bottom: @vw32-1160;
      }
      .text {
        padding-right: 0;
        margin-bottom: @vw50-1160;
      }
      .downloads {
        margin-top: @vw32-580;
        padding-right: 0;
        .download {
          margin-bottom: @vw10-1160;
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }
}
