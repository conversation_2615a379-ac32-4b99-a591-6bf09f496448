.reviewSliderBlock {
  &.inview {
    .slider {
      opacity: 1;
      transition-delay: .3s;
      transform: translateY(0);
    }
  }
  .titleWrapper, .navigation {
    width: 50%;
    display: inline-block;
    vertical-align: middle;
  }
  .navigation {
    text-align: right;
    .arrowButton {
      display: inline-block;
      vertical-align: middle;
      &:first-child {
        margin-right: @vw10;
      }
    }
  }
  .slider {
    margin-top: @vw70;
    white-space: nowrap;
    cursor: grab;
    opacity: 0;
    .transition(.3s);
    transform: translateY(@vw20);
    * {
      cursor: grab;
    }
     &.move {
       cursor: grabbing;
       * {
         cursor: grabbing;
       }
     }
    .innerSlider {
      left: 0;
      transition: transform 0.75s cubic-bezier(0.65, 0, 0.35, 1);
    }
    .slide {
      white-space: normal;
      display: inline-block;
      vertical-align: top;
      width: 50%;
      padding-right: @vw112 + @vw16;
      .author {
        width: (@vw112 * 3) + (@vw16 * 2);
      }
      .imageWrapper {
        display: inline-block;
        border-radius: 0 @vw20 0 @vw20;
        height: @vw50;
        width: @vw50;
        margin-right: @vw10;
        position: relative;
        vertical-align: middle;
        overflow: hidden;
        img {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          object-fit: cover;
          object-fit: center;
        }
      }
      .innerContent {
        display: inline-block;
        vertical-align: middle;
        width: calc(100% ~"-" @vw70);
        .textTitle {
          display: block;
        }
      }
      .normalTitle {
        margin-top: @vw20;
      }
      .textLink {
        margin-top: @vw40;
      }
    }
    .sliderNavigation {
      display: block;
      margin-top: @vw100;
      .navItem {
        display: inline-block;
        vertical-align: top;
        border-radius: 0 @vw50 0 @vw50;
        width: @vw112;
        height: @vw2 * 2;
        overflow: hidden;
        position: relative;
        background: @backgroundGrey;
        margin-right: @vw16;
        &:last-child {
          margin-right: 0;
        }
        &.active {
          &:after {
            width: 100%;
            transition-delay: .3s;
          }
        }
        &:after {
          content: '';
          height: 100%;
          top: 0;
          right: 0;
          position: absolute;
          background: @thirdColor;
          width: 0%;
          transition: width 0.6s cubic-bezier(0.87, 0, 0.13, 1);
        }
      }
    }
  }
}

@media all and (max-width: 1160px) {
  .reviewSliderBlock {
    &.inview {
      .slider {
        opacity: 1;
        transition-delay: .3s;
        transform: translateY(0);
      }
    }
    .navigation {
      .arrowButton {
        &:first-child {
          margin-right: @vw10-1160;
        }
      }
    }
    .slider {
      margin-top: @vw70-1160;
      transform: translateY(@vw20-1160);
      .slide {
        width: 50%;
        padding-right: @vw40-1160;
        .author {
          width: (@vw112-1160 * 3) + (@vw16-1160 * 2);
        }
        .imageWrapper {
          border-radius: 0 @vw20-1160 0 @vw20-1160;
          height: @vw50-1160;
          width: @vw50-1160;
          margin-right: @vw10-1160;
        }
        .innerContent {
          width: calc(100% ~"-" @vw70-1160);
        }
        .normalTitle {
          margin-top: @vw20-1160;
        }
        .textLink {
          margin-top: @vw40-1160;
        }
      }
      .sliderNavigation {
        margin-top: @vw100-1160;
        .navItem {
          border-radius: 0 @vw50-1160 0 @vw50-1160;
          width: @vw112-1160;
          height: @vw2-1160 * 2;
          margin-right: @vw16-1160;
          &:last-child {
            margin-right: 0;
          }
          &.active {
            &:after {
              transition-delay: .3s;
            }
          }
          &:after {
            background: @thirdColor;
          }
        }
      }
    }
  }
}

@media all and (max-width: 580px) {
  .reviewSliderBlock {
    &.inview {
      .slider {
        opacity: 1;
        transition-delay: .3s;
        transform: translateY(0);
      }
    }
    .titleWrapper {
      width: 75%;
    }
    .navigation {
      width: 25%;
      .arrowButton {
        &:first-child {
          margin-right: @vw10-580;
        }
      }
    }
    .slider {
      margin-top: @vw70-580;
      transform: translateY(@vw20-580);
      .slide {
        width: 100%;
        padding-right: @vw32-580;
        .author {
          width: (@vw112-580 * 3) + (@vw16-580 * 2);
        }
        .imageWrapper {
          border-radius: 0 @vw20-580 0 @vw20-580;
          height: @vw50-580;
          width: @vw50-580;
          margin-right: @vw10-580;
        }
        .innerContent {
          width: calc(100% ~"-" @vw70-580);
        }
        .normalTitle {
          margin-top: @vw20-580;
        }
        .textLink {
          margin-top: @vw40-580;
        }
      }
      .sliderNavigation {
        margin-top: @vw100-580;
        .navItem {
          border-radius: 0 @vw50-580 0 @vw50-580;
          width: @vw112-580;
          height: @vw2-580 * 2;
          margin-right: @vw16-580;
          &:last-child {
            margin-right: 0;
          }
          &.active {
            &:after {
              transition-delay: .3s;
            }
          }
          &:after {
            background: @thirdColor;
          }
        }
      }
    }
  }
}
