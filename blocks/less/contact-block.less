.contactBlock {
  .background {
    position: absolute;
    top: 0;
    left: 0;
    width: 60%;
    height: 100%;
    overflow: hidden;
    border-radius: 0 0 0 @vw100;
    img {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: cover;
      object-position: center;
      .transition(.3s);
    }
  }
  .cols {
    .col {
      display: inline-block;
      vertical-align: top;
      height: auto;
      width: 50%;
      &.formCol {
        background: @secondaryColor;
        border-radius: 0 @vw100;
        padding: @vw60 @vw50;
      }
    }
  }
  .titleWrapper {
    margin-bottom: @vw80;
    .subTitle, .normalTitle {
      color: @hardWhite;
    }
  }
  .formWrapper {
    margin-bottom: -@vw20;
    .field {
      vertical-align: top;
      display: block;
      margin-bottom: @vw20;
      &.half {
        display: inline-block;
        width: 50%;
        padding-right: @vw20;
      }
      &.submit {
        input {
          background-color: transparent;
          text-align: left !important;
          padding: 0;
          position: absolute;
          left: 0;
          top: auto;
          bottom: -@vw5;
          padding-right: @vw30;
          .transition(.3s);
          color: @thirdColor;
        }
        .textLink {
          &:hover {
            input {
              color: @primaryColor;
              padding-left: @vw30;
              padding-right: 0;
            }
          }
        }
      }
      label {
        display: block;
        margin-bottom: @vw10;
        color: @primaryColor;
        font-weight: 300;
        opacity: .5;
      }
      input, textarea {
        background-color: rgba(255,255,255,.2);
        font-family: 'Poppins', arial, sans-serif;
        font-size: @vw16;
        color: @primaryColor;
        font-weight: 400;
        line-height: @vw22;
        width: 100%;
        display: block;
        border: none;
        position: relative;
        overflow: hidden;
        text-overflow: ellipsis;
        padding: @vw10 @vw20;
      }
      input {
        height: @vw40;
        line-height: @vw40;
      }
      textarea {
        resize: none;
      }
      input[type=file]{
        padding: 0;
        line-height: 1.4;
        &::file-selector-button {
          background-color: transparent;
          position: relative;
          color: transparent;
          width: 100%;
          cursor: pointer;
          height: 100%;
          top: 0;
          line-height: @vw40;
          left: 0;
          border: 2px dashed @primaryColor;
          .transition(.3s);
          &:hover {
            opacity: .3;
          }
        }
      }
      .wpcf7-not-valid-tip {
        margin-top: @vw5;
      }
    }
  }
}

@media all and (max-width: 1160px) {
  .contactBlock {
    .background {
      border-radius: 0 0 0 @vw50-1160;
    }
    .cols {
      .col {
        width: 33.3333%;
        &.formCol {
          width: 66.6666%;
        }
        &.formCol {
          padding: @vw60-1160 @vw50-1160;
          border-radius: 0 @vw50-1160;
        }
      }
    }
    .titleWrapper {
      margin-bottom: @vw80-1160;
    }
    .formWrapper {
      margin-bottom: -@vw20-1160;
      .field {
        &.half {
          width: 100%;
          padding-right: @vw20-1160;
        }
        &.submit {
          input {
            padding-right: @vw30-1160;
          }
        }
        label {
          margin-bottom: @vw10-1160;
        }
        input, textarea {
          font-size: @vw16-1160;
          line-height: @vw22-1160;
          padding: @vw10-1160 @vw20-1160;
        }
        input {
          height: @vw40-1160;
          line-height: @vw40-1160;
        }
        input[type=file]{
          &::file-selector-button {
            line-height: @vw40-1160;
          }
        }
        .wpcf7-not-valid-tip {
          margin-top: @vw5-1160;
        }
      }
    }
  }
}

@media all and (max-width: 580px) {
  .contactBlock {
    .background {
      position: relative;
      display: block;
      height: 0;
      padding-bottom: 50%;
      width: 100%;
      border-radius: 0 @vw50-580 0 0;
    }
    .cols {
      .col {
        width: 100%;
        &:not(.formCol) {
          display: none;
          visibility: hidden;
        }
        &.formCol {
          width: 100%;
          padding: @vw60-580 @vw22-580;
          border-radius: 0 0 0 @vw50-580;
        }
      }
    }
    .titleWrapper {
      margin-bottom: @vw22-580;
    }
    .formWrapper {
      margin-bottom: -@vw20-580;
      .field {
        &.half {
          width: 100%;
          padding-right:0;
        }
        &.submit {
          input {
            padding-right: @vw30-580;
          }
        }
        label {
          margin-bottom: @vw10-580;
        }
        input, textarea {
          font-size: @vw21-580;
          line-height: @vw22-580;
          padding: @vw10-580 @vw20-580;
        }
        input {
          height: @vw40-580;
          line-height: @vw40-580;
        }
        input[type=file]{
          &::file-selector-button {
            line-height: @vw40-580;
          }
        }
        .wpcf7-not-valid-tip {
          margin-top: @vw5-580;
        }
      }
    }
  }
}
