.caseFooter<PERSON>lock {
  color: @hardWhite;
  &.secondary {
    &:before {
      opacity: 1;
    }
  }
  .col {
    display: inline-block;
    vertical-align: top;
    &:first-child {
      padding-right: @vw112 + (@vw16 * 2);
      width: (@vw112 * 4) + (@vw16 * 4);
    }
    &:last-child {
      width: (@vw112 * 8) + (@vw16 * 7);
    }
  }
  .textTitle {
    display: block;
    &.opacity {
      opacity: .6;
    }
  }
  .subTitle {
    margin-bottom: @vw40;
  }
  .relatedMembers {
    display: inline-block;
    vertical-align: top;
    width: (@vw112 * 2) + (@vw16 * 2);
    .relatedMember {
      display: block;
      width: 100%;
      margin-bottom: @vw10;
      .memberImage {
        display: inline-block;
        width: @vw50;
        height: @vw50;
        position: relative;
        vertical-align: middle;
        overflow: hidden;
        border-radius: 0 @vw20 0 @vw20;
        img {
          position: absolute;
          top: 0;
          left: 0;
          height: 100%;
          width: 100%;
          object-fit: cover;
          object-position: bottom;
        }
      }
      .innerContent {
        vertical-align: middle;
        display: inline-block;
        width: calc(100% ~"-" @vw50 ~"-" @vw5);
        padding-left: @vw10;
        span {
          display: block;
        }
      }
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
  .review {
    white-space: normal;
    display: inline-block;
    vertical-align: top;
    width: 100%;
    padding-right: (@vw112 * 3) + (@vw16 * 3);
    .author {
      width: (@vw112 * 3) + (@vw16 * 2);
    }
    .imageWrapper {
      display: inline-block;
      border-radius: 0 @vw20 0 @vw20;
      height: @vw50;
      width: @vw50;
      position: relative;
      vertical-align: middle;
      overflow: hidden;
      img {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-fit: center;
      }
    }
    .innerContent {
      display: inline-block;
      padding-left: @vw20;
      vertical-align: middle;
      width: calc(100% ~"-" @vw60);
    }
    .normalTitle {
      margin-bottom: @vw40;
    }
  }
}

@media all and (max-width: 1160px) {
  .caseFooterBlock {
    .contentWrapper {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
    }
    .col {
      &:first-child {
        padding-right: @vw32-1160;
        width: (@vw112-1160 * 2) + (@vw16-1160 * 2);
      }
      &:last-child {
        width: (@vw112-1160 * 5) + (@vw16-1160 * 6);
      }
    }
    .subTitle {
      margin-bottom: @vw40-1160;
    }
    .relatedMembers {
      width: (@vw112-1160 * 2) + (@vw16-1160 * 2);
      .relatedMember {
        margin-bottom: @vw10-1160;
        .memberImage {
          width: @vw50-1160;
          height: @vw50-1160;
        }
        .innerContent {
          width: calc(100% ~"-" @vw50-1160 ~"-" @vw5-1160);
          padding-left: @vw10-1160;
        }
      }
    }
    .review {
      padding-right: 0;
      .author {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        width: (@vw112-1160 * 3) + (@vw16-1160 * 2);
      }
      .imageWrapper {
        height: @vw50-1160;
        width: @vw50-1160;
      }
      .innerContent {
        padding-left: @vw20-1160;
        width: calc(100% ~"-" @vw60-1160);
      }
      .normalTitle {
        margin-bottom: @vw40-1160;
      }
    }
  }
}

@media all and (max-width: 580px) {
  .caseFooterBlock {
    .contentWrapper {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
    }
    .col {
      &:first-child {
        padding-right: 0;
        width: 100%;
      }
      &:last-child {
        margin-top: @vw50-580;
        width: 100%;
      }
    }
    .subTitle {
      margin-bottom: @vw40-580;
    }
    .relatedMembers {
      width: (@vw112-580 * 2) + (@vw16-580 * 2);
      .relatedMember {
        margin-bottom: @vw10-580;
        .memberImage {
          width: @vw50-580;
          height: @vw50-580;
        }
        .innerContent {
          width: calc(100% ~"-" @vw50-580 ~"-" @vw5-580);
          padding-left: @vw10-580;
        }
      }
    }
    .review {
      padding-right: 0;
      .author {
        width: (@vw112-580 * 3) + (@vw16-580 * 2);
      }
      .imageWrapper {
        height: @vw50-580;
        width: @vw50-580;
      }
      .innerContent {
        padding-left: @vw20-580;
        width: calc(100% ~"-" @vw60-580);
      }
      .normalTitle {
        margin-bottom: @vw22-580;
      }
    }
  }
}
