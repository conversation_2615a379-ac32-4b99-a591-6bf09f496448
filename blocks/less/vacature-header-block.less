.vacatureHeaderBlock {
  &:first-child {
    margin-top: @vw100 + @vw20;
  }
  .top {
    margin-bottom: @vw100 + @vw20;
    .col {
      &:first-child {
        width: (@vw112 * 8) + (@vw16 * 7);
      }
      &:last-child {
        width: (@vw112 * 4) + (@vw16 * 4);
      }
    }
  }
  .bottom {
    .col {
      &:first-child {
        padding-right: @vw112 + (@vw16 * 2);
        width: (@vw112 * 4) + (@vw16 * 4);
      }
      &:last-child {
        width: (@vw112 * 8) + (@vw16 * 7);
      }
    }
  }
  .subTitle {
    margin-bottom: @vw20;
  }
  .titleWrapper {
    margin-bottom: @vw60;
  }
  .textWrapper {
    display: inline-block;
    width: 100%;
    .text {
      display: inline-block;
      vertical-align: top;
      width: calc(100% ~"-" (@vw112 * 2) + (@vw16 * 2));
    }
  }
  .col {
    display: inline-block;
    vertical-align: top;
  }
  .textLink {
    margin-bottom: @vw60;
  }
  .imageWrapper {
    border-radius: 0 @vw100 0 @vw100;
    margin-top: @vw50;
    overflow: hidden;
    position: relative;
    height: auto;
    display: inline-block;
    vertical-align: top;
    width: (@vw112 * 2) + @vw16;
    .innerImage {
      position: relative;
      height: 0;
      padding-bottom: 130.70652173913044%;
      width: 100%;
      position: relative;
      .animateImage, img {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        width: 100%;
      }
      img {
        object-fit: cover;
        object-position: bottom;
      }
    }
  }
}

@media all and (max-width: 1160px) {
  .vacatureHeaderBlock {
    &:first-child {
      margin-top: @vw100-1160 + @vw20-1160;
    }
    .top, .bottom {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
    }
    .top {
      margin-bottom: @vw100-1160 + @vw20-1160;
      .col {
        &:first-child {
          width: (@vw112-1160 * 5) + (@vw16-1160 * 5);
        }
        &:last-child {
          width: (@vw112-1160 * 2) + (@vw16-1160 * 3);
        }
      }
    }
    .bottom {
      .col {
        &:first-child {
          padding-right: @vw112-1160 + (@vw16-1160 * 2);
          width: (@vw112-1160 * 2) + (@vw16-1160 * 3);
        }
        &:last-child {
          width: (@vw112-1160 * 5) + (@vw16-1160 * 6);
        }
      }
    }
    .subTitle {
      margin-bottom: @vw20-1160;
    }
    .titleWrapper {
      margin-bottom: @vw60-1160;
    }
    .textWrapper {
      display: inline-block;
      width: 100%;
      .text {
        display: inline-block;
        vertical-align: top;
        width: calc(100% ~"-" (@vw112-1160 * 2) + (@vw16-1160 * 2));
      }
    }
    .col {
      display: inline-block;
      vertical-align: top;
    }
    .textLink {
      margin-bottom: @vw60-1160;
    }
    .imageWrapper {
      border-radius: 0 @vw100-1160 0 @vw100-1160;
      margin-top: @vw50-1160;
      overflow: hidden;
      position: relative;
      height: auto;
      display: inline-block;
      vertical-align: top;
      width: (@vw112-1160 * 2) + @vw16-1160;
      .innerImage {
        position: relative;
        height: 0;
        padding-bottom: 130.70652173913044%;
        width: 100%;
        position: relative;
        .animateImage, img {
          position: absolute;
          top: 0;
          left: 0;
          height: 100%;
          width: 100%;
        }
        img {
          object-fit: cover;
          object-position: bottom;
        }
      }
    }
  }
}

@media all and (max-width: 580px) {
  .vacatureHeaderBlock {
    &:first-child {
      margin-top: 0;
    }
    .top {
      margin-bottom: @vw50-580;
      .col {
        &:first-child {
          margin-bottom: @vw22-580;
          width: 100%;
        }
        &:last-child {
          width: 100%;
        }
      }
    }
    .bottom {
      .col {
        &:first-child {
          padding-right: @vw112-580 + (@vw16-580 * 2);
          width: 100%;
        }
        &:last-child {
          width: 100%;
        }
      }
    }
    .subTitle {
      margin-bottom: @vw20-580;
    }
    .titleWrapper {
      margin-bottom: @vw60-580;
    }
    .textWrapper {
      width: 100%;
      .text {
        width: calc(100% ~"-" (@vw112-580 * 2) + (@vw16-580 * 2));
      }
    }
    .textLink {
      margin-bottom: 0;
    }
    .imageWrapper {
      margin-bottom: @vw32-580;
      border-radius: 0 @vw100-580 0 @vw100-580;
      margin-top: @vw22-580;
      width: (@vw112-580 * 2) + @vw16-580;
      .innerImage {
        padding-bottom: 130.70652173913044%;
      }
    }
  }
}
