.servicesHeaderBlock {
  &:first-child {
    margin-top: @vw100 + @vw20;
  }
  .textWrapper {
    padding-left: @vw112 + (@vw16 * 2);
    display: inline-block;
    vertical-align: middle;
    width: (@vw112 * 7) + (@vw16 * 7);
    .text {
      max-width: 100%;
      margin-top: @vw60;
      width: (@vw112 * 4) + (@vw16 * 3);
    }
    .button {
      margin-top: @vw30;
    }
  }
  .imageWrapper {
    border-radius: 0 @vw100 0 @vw100;
    overflow: hidden;
    position: relative;
    height: auto;
    display: inline-block;
    vertical-align: middle;
    width: (@vw112 * 5) + (@vw16 * 4);
    .innerImage {
      position: relative;
      height: 0;
      padding-bottom: 130.70652173913044%;
      width: 100%;
      position: relative;
      .animateImage, img {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        width: 100%;
      }
      img {
        object-fit: cover;
        object-position: bottom;
      }
    }
  }
}
@media all and (max-width: 1160px) {
  .servicesHeaderBlock {
    &:first-child {
      margin-top: @vw100-1160 + @vw20-1160;
    }
    .textWrapper {
      padding-left: @vw112-1160;
      display: inline-block;
      vertical-align: middle;
      width: (@vw112-1160 * 4) + (@vw16-1160 * 4);
      .text {
        margin-top: @vw60-1160;
        width: (@vw112-1160 * 4) + (@vw16-1160 * 3);
      }
      .button {
        margin-top: @vw30-1160;
      }
    }
    .imageWrapper {
      width: (@vw112-1160 * 3) + (@vw16-1160 * 2);
      .innerImage {
        padding-bottom: 130.70652173913044% - 1160;
      }
    }
  }
}

@media all and (max-width: 580px) {
  .servicesHeaderBlock {
    &:first-child {
      margin-top: @vw100-580 + @vw20-580;
    }
    .textWrapper {
      margin-top: @vw50-580;
      padding-left: 0;
      display: block;
      width: 100%;
      .text {
        margin-top: @vw32-580;
        width: 100%;
      }
      .button {
        margin-top: @vw30-580;
      }
    }
    .imageWrapper {
      width: 100%;
      .innerImage {
        padding-bottom: 130.70652173913044% - 580;
      }
    }
  }
}
