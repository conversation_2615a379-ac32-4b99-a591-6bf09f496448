.serviceHeaderBlock {
  &:first-child {
    margin-top: @vw100 + @vw20;
  }
  .textWrapper {
    padding-left: @vw112 + (@vw16 * 2);
    display: inline-block;
    vertical-align: middle;
    width: (@vw112 *8) + (@vw16 * 8);
    .text {
      margin-top: @vw60;
      max-width: 100%;
      width: (@vw112 * 4) + (@vw16 * 3);
    }
    .button {
      margin-top: @vw30;
    }
  }
  .imageWrapper {
    border-radius: 0 @vw100 0 @vw100;
    overflow: hidden;
    position: relative;
    height: auto;
    display: inline-block;
    vertical-align: middle;
    width: (@vw112 * 4) + (@vw16 * 3);
    .innerImage {
      position: relative;
      height: 0;
      padding-bottom: 130.70652173913044%;
      width: 100%;
      position: relative;
      .animateImage, img {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        width: 100%;
      }
      img {
        object-fit: cover;
        object-position: bottom;
      }
    }
  }
  .usps {
    margin-top: @vw20;
    max-width: 100%;
    width: (@vw112 * 4) + (@vw16 * 3);
    .usp {
      padding: @vw10 0 @vw10 @vw40;
      display: block;
      width: 100%;
      border-bottom: 1px solid @grey;
      position: relative;
      &:before {
        color: @secondaryColor;
        font-family: "icomoon";
        content: "\e90c";
        position: absolute;
        top: @vw10;
        left: 0;
      }
    }
  }
}

@media all and (max-width: 1160px) {
  .serviceHeaderBlock {
    &:first-child {
      margin-top: @vw100-1160 + @vw20-1160;
    }
    .textWrapper {
      padding-left: @vw112-1160;
      width: (@vw112-1160 * 4) + @vw16-1160;
      .text {
        margin-top: @vw60-1160;
        width: 100%;
      }
      .button {
        margin-top: @vw30-1160;
      }
    }
    .imageWrapper {
      width: (@vw112-1160 * 4) + (@vw16-1160 * 3);
      .innerImage {
        padding-bottom: 130.70652173913044%;
      }
    }
    .usps {
      width: (@vw112-1160 * 4) + (@vw16-1160 * 3);
      .usp {
        padding: @vw10-1160 0 @vw10-1160 @vw40-1160;
        &:before {
          font-size: @vw20-1160; // Adjust icon size for smaller screens
        }
      }
    }
  }
}

@media all and (max-width: 580px) {
  .serviceHeaderBlock {
    &:first-child {
      margin-top: @vw100-580 + @vw20-580;
    }
    .textWrapper {
      padding-left: 0;
      width: 100%;
      .text {
        margin-top: @vw60-580;
        width: (@vw112-580 * 4) + (@vw16-580 * 3);
      }
      .button {
        margin-top: @vw30-580;
      }
    }
    .imageWrapper {
      width: (@vw112-580 * 4) + (@vw16-580 * 3);
      .innerImage {
        padding-bottom: 130.70652173913044%;
      }
    }
    .usps {
      width: (@vw112-580 * 4) + (@vw16-580 * 3);
      .usp {
        padding: @vw10-580 0 @vw10-580 @vw40-580;
        &:before {
          font-size: @vw20-580; // Adjust icon size for very small screens
        }
      }
    }
  }
}
