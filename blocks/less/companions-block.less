.companionsBlock {
  .col {
    display: inline-block;
    vertical-align: middle;
    &:first-child {
      width: 75%;
    }
    &:last-child {
      width: 25%;
      text-align: right;
    }
  }
  .companions {
    margin-left: -@vw16 / 2;
    margin-top: @vw80;
    margin-bottom: -@vw50;
    width: calc(100% ~"+" @vw16);
    .companion {
      color: @hardBlack;
      text-decoration: none;
      display: inline-block;
      vertical-align: top;
      margin: 0 @vw16 / 2;
      margin-bottom: @vw50;
      width: calc(33.3333% ~"-" @vw16);
      &.link {
        cursor: pointer;
        .transition(.3s);
        &:hover {
          color: @secondaryColor;
        }
      }
      .titleWrapper {
        .normalTitle {
          height: @vw44;
          overflow: hidden;
          position: relative;
          display: inline-block;
          vertical-align: middle;
          width: calc(100% ~"-" @vw112);
          text-overflow: ellipsis;
        }
      }
      .bottom {
        display: flex;
        flex-wrap: wrap;
        .social {
          font-size: @vw16;
          padding-right: @vw5;
          display: inline-block;
          width: @vw20;
        }
        .subTitle {
          display: inline-block;
          width: calc(100% ~"-" @vw20);
        }
      }
      .textLink {
        margin-top: @vw20;
      }
    }
  }
}

@media all and (max-width: 1160px) {
  .companionsBlock {
    .col {
      &:first-child {
        width: 60%;
      }
      &:last-child {
        width: 40%;
      }
    }
    .companions {
      margin-left: -@vw16-1160 / 2;
      margin-top: @vw80-1160;
      margin-bottom: -@vw70-1160;
      width: calc(100% + @vw16-1160);
      .companion {
        margin: 0 @vw16-1160 / 2;
        margin-bottom: @vw70-1160;
        width: calc(33.3333% ~"-" @vw16-1160);
        .titleWrapper {
          .normalTitle {
            height: @vw32-1160;
            width: calc(100% ~"-" @vw112-1160);
          }
        }
        .bottom {
          .social {
            font-size: @vw16-1160;
            padding-right: @vw5-1160;
            width: @vw20-1160;
          }
          .subTitle {
            width: calc(100% ~"-" @vw20-1160);
          }
        }
      }
    }
  }
}

@media all and (max-width: 580px) {
  .companionsBlock {
    .col {
      &:first-child {
        width: 60%;
      }
      &:last-child {
        width: 40%;
      }
    }
    .companions {
      margin-left: -@vw16-580 / 2;
      margin-top: @vw80-580;
      margin-bottom: -@vw70-580;
      width: calc(100% + @vw16-580);
      .companion {
        margin: 0 @vw16-580 / 2;
        margin-bottom: @vw70-580;
        width: calc(50% ~"-" @vw16-580);
        .titleWrapper {
          margin-bottom: @vw10-580;
          .normalTitle {
            height: @vw32-580;
            width: calc(100% ~"-" @vw112-580);
          }
        }
        .bottom {
          .social {
            font-size: @vw21-580;
            padding-right: @vw5-580;
            width: @vw20-580;
          }
          .subTitle {
            padding-left: @vw10-580;
            width: calc(100% ~"-" @vw20-580);
          }
        }
      }
    }
  }
}
