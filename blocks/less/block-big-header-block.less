.bigHeaderBlock {
  margin-top: 0;
  padding-top: @vw87;
  .blueBlock {
    background: @blue;
    height: calc(50vh ~"-" @vw114);
    width: 100%;
    position: absolute;
    top: 0;
    overflow: hidden;
    .svgWrapper {
      height: 150%;
      position: absolute;
      top: -50%;
      left: -25%;
      width: 150%;
    }

    .animWrapper {
      text-align: center;
      animation: rotate 240s linear infinite;
      svg {
        height: auto;
        width: 100%;
        // top:50%;
        // left: 50%;
        // transform: translateX(-50%) translateY(-50%);
        path {
          fill: @hardWhite;
          opacity: .1;
        }
      }
    }
  }
  .background {
    border-radius: @vw40;
    height: calc(100vh ~"-" @vw87 ~"-" @vw87);
    overflow: hidden;
    position: relative;
    width: 100%;
    &:after {
      border-radius: @vw40;
      content: '';
      height: 100%;
      position: absolute;
      opacity: .3;
      left: 0;
      top: 0;
      width: 100%;
      background: @darker<PERSON>rey;
    }
    .image, .video {
      position: absolute;
      top: 0;
      width: 100%;
      height: 100%;
      left: 0;
      object-fit: cover;
    }
  }
  .textWrapper {
    padding: 0 (@vw99) + (@vw22 * 2);
    position: absolute;
    left: 0;
    height: auto;
    width: 100%;
    bottom: @vw80;
    z-index: 2;
    .roundButton {
      position: absolute;
      right: (@vw99) + (@vw22 * 2);
      bottom: 0;
    }
    .button {
      margin-left: @vw20;
      &:first-child {
        margin-left: 0;
      }
    }
  }
  .contentWrapper {
    .link {
      color: @hardWhite;
      cursor: pointer;
      display: inline-block;
      margin-bottom: @vw20;
      .transition(.3s);
      &:hover {
        opacity: .6;
      }
      i {
        cursor: pointer;
        padding-right: @vw5;
      }
    }
    .bigTitle {
      color: @almostWhite;
      margin-bottom: @vw20;
      max-width: @vw748;
      text-transform: uppercase;
    }
  }
}

@keyframes rotate {
  to {
    transform: rotate(360deg);
  }
}

@media all and (max-width: 1160px) {
  .bigHeaderBlock {
    padding-top: @vw87-1160;
    .blueBlock {
      height: calc(50vh ~"-" @vw114-1160);
    }
    .background {
      border-radius: @vw40-1160;
      height: calc(100vh ~"-" @vw87-1160 ~"-" @vw87-1160);
      &:after {
        border-radius: @vw40-1160;
      }
    }
    .textWrapper {
      padding: 0 @vw80-1160;
      bottom: @vw80-1160;
      .roundButton {
        right: @vw80-1160;
      }
      .button {
        margin-left: @vw20-1160;
      }
    }
    .contentWrapper {
      .link {
        margin-bottom: @vw20-1160;
        i {
          padding-right: @vw5-1160;
        }
      }
      .bigTitle {
        margin-bottom: @vw20-1160;
        max-width: @vw748-1160;
      }
    }
  }
}

@media all and (max-width: 580px) {
  .bigHeaderBlock {
    padding-top: @vw87-580;
    .blueBlock {
      height: calc(70vh ~"-" @vw114-580);
    }
    .background {
      border-radius: @vw40-580;
      height: calc(100vh ~"-" @vw87-580 ~"-" @vw87-580);
      &:after {
        border-radius: @vw40-580;
      }
    }
    .textWrapper {
      padding: 0 @vw50-580;
      padding-right: @vw50-580;
      bottom: @vw50-580;
      .roundButton {
        bottom: @vw5-580;
        right: @vw22-580;
      }
      .button {
        margin-left: @vw20-580;
      }
    }
    .contentWrapper {
      .link {
        margin-bottom: @vw20-580;
        i {
          padding-right: @vw5-580;
        }
      }
      .bigTitle {
        margin-bottom: @vw20-580;
        max-width: 100%;
      }
    }
  }
}
