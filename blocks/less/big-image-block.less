.bigImageBlock {
  .imageWrapper {
    overflow: hidden;
    position: relative;
    height: auto;
    display: inline-block;
    vertical-align: top;
    width: 100%;
    .innerImage {
      position: relative;
      height: 0;
      padding-bottom: 43.15789473684211%;
      width: 100%;
      position: relative;
      img {
        position: absolute;
        top: -20%;
        left: 0;
        height: 120%;
        width: 100%;
        object-fit: cover;
        object-position: center;
      }
    }
  }
}

@media all and (max-width: 580px) {
  .bigImageBlock {
    .imageWrapper {
      .innerImage {
        padding-bottom: 100%;
        img {
          position: absolute;
          top: 0;
          transform: translate3d(0,0,0) !important;
          height: 100%;
        }
      }
    }
  }
}
