.updatesBlock {
  .imageCursor {
    width: (@vw112 * 4) + (@vw16 * 3);
    height: @vw100 * 2.8;
    opacity: .2;
    pointer-events: none;
    position: absolute;
    left: 0;
    top: 0;
    .innerImage {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
    img {
      object-fit: cover;
      object-position: center;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
  }
  .filters {
    .col {
      display: inline-block;
      vertical-align: middle;
      &:first-child {
        width: 25%;
      }
      &:last-child {
        width: 75%;
        text-align: right;
      }
    }
  }
  .updates {
    margin-top: @vw40 + @vw40;
    .update {
      cursor: pointer;
      position: relative;
      display: block;
      padding: @vw40 0 @vw60 0;
      text-decoration: none;
      color: @primaryColor;
      .transition(.3s);
      &.remove {
        display: none;
      }
      &:hover {
        color: @secondaryColor;
        .col {
          .normalTitle {
            padding-left: @vw20;
            padding-right: 0;
            transition-delay: .15s;
          }
        }
        &:before {
          width: 0% !important;
          .transitionMore(width, .3s, .15s, ease-in-out);
        }
        &:after {
          width: 100%;
          transition-delay: .15s;
        }
      }
      &:before, &:after {
        content: '';
        top: auto;
        bottom: 0;
        position: absolute;
        right: 0;
        height: 1px;
        background: @primaryColor;
        transition-delay: .15s;
        transition: width 0.6s cubic-bezier(0.83, 0, 0.17, 1);
        width: 100%;
      }
      &:after {
        right: auto;
        left: 0;
        background: @thirdColor;
        width: 0%;
        transition-delay: 0;
      }
      * {
        cursor: pointer;
      }
      .col {
        display: inline-block;
        vertical-align: top;
        &:nth-child(1), &:nth-child(2), &:nth-child(4) {
          opacity: .5;
          width: (@vw112 * 2) + (@vw16 * 2);
        }
        &:nth-child(3) {
          padding-right: @vw16;
          width: (@vw112 * 5) + (@vw16 * 5);
        }
        .normalTitle {
          height: @vw44 * 2;
          overflow: hidden;
          width: 100%;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          padding-right: @vw20;
          .transition(.3s);
        }
      }
    }
    .noResults {
      background: @hardWhite;
      padding: @vw20;
    }
  }
}

@media all and (max-width: 1160px) {
  .updatesBlock {
    .imageCursor {
      width: (@vw112-1160 * 4) + (@vw16-1160 * 3);
      height: @vw100-1160 * 2.8;
    }
    .filters {
      .col {
        &:first-child, &:last-child {
          display: block;
          width: 100%;
        }
        &:first-child {
          margin-bottom: @vw20-1160;
        }
        &:last-child {
          text-align: left;
        }
      }
    }
    .updates {
      margin-top: @vw40-1160 + @vw40-1160;
      .update {
        margin-top: @vw40 + @vw40;
        transform: translateY(@vw20);
        .col {
          &:nth-child(1), &:nth-child(2), &:nth-child(4) {
            width: (@vw112-1160 * 1) + (@vw16-1160 * 2);
          }
          &:nth-child(3) {
            padding-right: @vw16-1160;
            width: (@vw112-1160 * 4) + (@vw16-1160 * 4);
          }
          .normalTitle {
            padding-right: @vw20-1160;
            height: @vw32-1160 * 2;
          }
        }
      }
    }
  }
}

@media all and (max-width: 580px) {
  .updatesBlock {
    .imageCursor {
      width: (@vw112-580 * 4) + (@vw16-580 * 3);
      height: @vw100-580 * 2.8;
    }
    .filters {
      .col {
        &:first-child, &:last-child {
          display: block;
          width: 100%;
        }
        &:first-child {
          margin-bottom: @vw20-580;
        }
        &:last-child {
          text-align: left;
        }
      }
    }
    .select2-container {
      width: 100% !important;
      margin-bottom: @vw10-580;
      &:last-child {
        margin-bottom: 0;
      }
    }
    .updates {
      margin-top: @vw40-580 + @vw40-580;
      .update {
        margin-top: @vw40 + @vw40;
        transform: translateY(@vw20);
        .col {
          &:nth-child(1), &:nth-child(2), &:nth-child(4) {
            width: (@vw112-580 * 1) + (@vw16-580 * 2);
          }
          &:nth-child(3) {
            margin-top: @vw10-580;
            padding-right: @vw16-580;
            width: 100%;
          }
          .normalTitle {
            padding-right: @vw20-580;
            height: @vw32-580 * 2;
          }
        }
      }
    }
  }
}
