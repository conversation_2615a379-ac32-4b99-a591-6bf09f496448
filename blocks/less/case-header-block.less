.caseHeaderBlock {
  .subTitle {
    margin-bottom: @vw20;
  }
  .titleWrapper {
    margin-bottom: @vw60;
  }

  .textWrapper {
    display: inline-block;
    vertical-align: middle;
    padding-right: @vw16;
    width: (@vw112 * 7) + (@vw16 * 7);
    .relatedMembers {
      display: inline-block;
      vertical-align: top;
      width: (@vw112 * 2) + (@vw16 * 2);
    }
  }
  .imageWrapper {
    border-radius: 0 @vw100 0 @vw100;
    overflow: hidden;
    position: relative;
    height: auto;
    display: inline-block;
    vertical-align: middle;
    width: (@vw112 * 5) + (@vw16 * 4);
    .innerImage {
      position: relative;
      height: 0;
      padding-bottom: 130.70652173913044%;
      width: 100%;
      position: relative;
      .animateImage, img {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        width: 100%;
      }
      img {
        object-fit: cover;
        object-position: bottom;
      }
    }
  }
}

@media all and (max-width: 1160px) {
  .caseHeaderBlock {
    .contentWrapper {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      align-items: center;
    }
    .subTitle {
      margin-bottom: @vw20-1160;
    }
    .titleWrapper {
      margin-bottom: @vw60-1160;
    }
    .textWrapper {
      padding-right: @vw50-1160;
      width: (@vw112-1160 * 4) + (@vw16-1160 * 5);
      .relatedMembers {
        width: (@vw112-1160 * 2) + (@vw16-1160 * 2);
      }
    }
    .imageWrapper {
      width: (@vw112-1160 * 3) + (@vw16-1160 * 3);
      .innerImage {
        padding-bottom: 130.70652173913044%;
      }
    }
  }
}

@media all and (max-width: 580px) {
  .caseHeaderBlock {
    .subTitle {
      margin-bottom: @vw20-580;
    }
    .titleWrapper {
      margin-bottom: @vw60-580;
    }
    .textWrapper {
      padding-right: @vw16-580;
      width: 100%;
      .relatedMembers {
        width: 100%;
      }
    }
    .imageWrapper {
      width: 100%;
      .innerImage {
        padding-bottom: 100%;
      }
    }
  }
}
