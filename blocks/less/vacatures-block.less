.vacaturesBlock {
  .filters {
    .col {
      display: inline-block;
      vertical-align: middle;
      &:first-child {
        width: 25%;
      }
      &:last-child {
        width: 75%;
        text-align: right;
      }
    }
  }
  .vacatures {
    .vacature {
      cursor: pointer;
      position: relative;
      display: block;
      padding: @vw20 0 @vw40 0;
      text-decoration: none;
      color: @primaryColor;
      .transition(.3s);
      &.remove {
        display: none;
      }
      &:hover {
        color: @secondaryColor;
        .col {
          .normalTitle {
            padding-left: @vw20;
            padding-right: 0;
            transition-delay: .15s;
          }
        }
        &:before {
          width: 0% !important;
          .transitionMore(width, .3s, .15s, ease-in-out);
        }
        &:after {
          width: 100%;
          transition-delay: .15s;
        }
      }
      &:before, &:after {
        content: '';
        top: auto;
        bottom: 0;
        position: absolute;
        right: 0;
        height: 1px;
        background: @primaryColor;
        transition-delay: .15s;
        transition: width 0.6s cubic-bezier(0.83, 0, 0.17, 1);
        width: 100%;
      }
      &:after {
        right: auto;
        left: 0;
        background: @thirdColor;
        width: 0%;
        transition-delay: 0;
      }
      * {
        cursor: pointer;
      }
      .col {
        display: inline-block;
        vertical-align: top;
        &:nth-child(1), &:nth-child(2){
          opacity: .5;
          width: (@vw112 * 2) + (@vw16 * 2);
        }
        &:nth-child(3) {
          padding-right: @vw16;
          width: (@vw112 * 7) + (@vw16 * 6);
        }
        .normalTitle {
          height: @vw44;
          overflow: hidden;
          width: 100%;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
          padding-right: @vw20;
          .transition(.3s);
        }
      }
      i {
        position: absolute;
        top: auto;
        bottom: @vw10;
        right: 0;
      }
    }
    .noResults {
      background: @hardWhite;
      padding: @vw20;
    }
  }
}

@media all and (max-width: 1160px) {
  .vacaturesBlock {
    .filters {
      .col {
        display: inline-block;
        vertical-align: middle;
        &:first-child {
          width: 25%;
        }
        &:last-child {
          width: 75%;
          text-align: right;
        }
      }
    }
    .vacatures {
      .vacature {
        cursor: pointer;
        position: relative;
        display: block;
        padding: @vw20-1160 0 @vw40-1160 0;
        text-decoration: none;
        color: @primaryColor;
        .transition(.3s);
        &.remove {
          display: none;
        }
        &:hover {
          color: @secondaryColor;
          .col {
            .normalTitle {
              padding-left: @vw20-1160;
              padding-right: 0;
              transition-delay: .15s;
            }
          }
          &:before {
            width: 0% !important;
            .transitionMore(width, .3s, .15s, ease-in-out);
          }
          &:after {
            width: 100%;
            transition-delay: .15s;
          }
        }
        &:before, &:after {
          content: '';
          top: auto;
          bottom: 0;
          position: absolute;
          right: 0;
          height: 1px;
          background: @primaryColor;
          transition-delay: .15s;
          transition: width 0.6s cubic-bezier(0.83, 0, 0.17, 1);
          width: 100%;
        }
        &:after {
          right: auto;
          left: 0;
          background: @thirdColor;
          width: 0%;
          transition-delay: 0;
        }
        * {
          cursor: pointer;
        }
        .col {
          display: inline-block;
          vertical-align: top;
          &:nth-child(1), &:nth-child(2){
            opacity: .5;
            width: (@vw112-1160 * 1) + (@vw16-1160 * 2);
          }
          &:nth-child(3) {
            padding-right: @vw16-1160;
            width: (@vw112-1160 * 5) + (@vw16-1160 * 6);
          }
          .normalTitle {
            height: @vw44-1160;
            overflow: hidden;
            width: 100%;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            padding-right: @vw20-1160;
            .transition(.3s);
          }
        }
        i {
          position: absolute;
          top: auto;
          bottom: @vw10-1160;
          right: 0;
        }
      }
      .noResults {
        background: @hardWhite;
        padding: @vw20-1160;
      }
    }
  }
}

@media all and (max-width: 580px) {
  .vacaturesBlock {
    .filters {
      .col {
        display: inline-block;
        vertical-align: middle;
        &:first-child {
          width: 25%;
        }
        &:last-child {
          width: 75%;
          text-align: right;
        }
      }
    }
    .vacatures {
      .vacature {
        cursor: pointer;
        position: relative;
        display: block;
        padding: @vw20-580 0 @vw40-580 0;
        text-decoration: none;
        color: @primaryColor;
        .transition(.3s);
        &.remove {
          display: none;
        }
        &:hover {
          color: @secondaryColor;
          .col {
            .normalTitle {
              padding-left: @vw20-580;
              padding-right: 0;
              transition-delay: .15s;
            }
          }
          &:before {
            width: 0% !important;
            .transitionMore(width, .3s, .15s, ease-in-out);
          }
          &:after {
            width: 100%;
            transition-delay: .15s;
          }
        }
        &:before, &:after {
          content: '';
          top: auto;
          bottom: 0;
          position: absolute;
          right: 0;
          height: 1px;
          background: @primaryColor;
          transition-delay: .15s;
          transition: width 0.6s cubic-bezier(0.83, 0, 0.17, 1);
          width: 100%;
        }
        &:after {
          right: auto;
          left: 0;
          background: @thirdColor;
          width: 0%;
          transition-delay: 0;
        }
        * {
          cursor: pointer;
        }
        .col {
          display: inline-block;
          vertical-align: top;
          &:nth-child(1), &:nth-child(2){
            opacity: .5;
            width: (@vw112-580 * 2) + (@vw16-580 * 2);
          }
          &:nth-child(3) {
            margin-top: @vw22-580;
            padding-right: @vw16-580;
            width: 100%;
          }
          .normalTitle {
            height: @vw44-580;
            overflow: hidden;
            width: 100%;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            padding-right: @vw20-580;
            .transition(.3s);
          }
        }
        i {
          position: absolute;
          top: auto;
          bottom: @vw10-580;
          right: 0;
        }
      }
      .noResults {
        background: @hardWhite;
        padding: @vw20-580;
      }
    }
  }
}
