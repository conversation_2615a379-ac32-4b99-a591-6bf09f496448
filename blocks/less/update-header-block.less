.updateHeaderBlock {
  .subTitle {
    margin-bottom: @vw20;
  }
  .titleWrapper {
    margin-bottom: @vw60;
  }
  .textWrapper {
    display: inline-block;
    width: 100%;
    .relatedMembers {
      display: inline-block;
      vertical-align: top;
      width: (@vw112 * 2) + (@vw16 * 2);
      .relatedMember {
        display: block;
        width: 100%;
        margin-bottom: @vw10;
        .memberImage {
          display: inline-block;
          width: @vw50;
          height: @vw50;
          position: relative;
          vertical-align: middle;
          overflow: hidden;
          border-radius: 0 @vw20 0 @vw20;
          img {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            width: 100%;
            object-fit: cover;
            object-position: bottom;
          }
        }
        .innerContent {
          vertical-align: middle;
          display: inline-block;
          width: calc(100% ~"-" @vw50 ~"-" @vw5);
          padding-left: @vw10;
          span {
            display: block;
          }
        }
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
    .text {
      display: inline-block;
      vertical-align: top;
      max-width: 100%;
      width: calc(100% ~"-" (@vw112 * 2) + (@vw16 * 2));
    }
  }
  .col {
    display: inline-block;
    vertical-align: top;
    &:first-child {
      padding-right: @vw112 + @vw16;
      width: (@vw112 * 7) + (@vw16 * 7);
    }
    &:last-child {
      width: (@vw112 * 5) + (@vw16 * 4);
    }
  }
  .textLink {
    margin-bottom: @vw60;
  }
  .imageWrapper {
    border-radius: 0 @vw100 0 @vw100;
    overflow: hidden;
    position: relative;
    height: auto;
    display: inline-block;
    vertical-align: top;
    width: 100%;
    .innerImage {
      position: relative;
      height: 0;
      padding-bottom: 130.70652173913044%;
      width: 100%;
      position: relative;
      .animateImage, img {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        width: 100%;
      }
      img {
        object-fit: cover;
        object-position: bottom;
      }
    }
  }
}

@media all and (max-width: 1160px) {
  .updateHeaderBlock {
    .contentWrapper {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
    }
    .textWrapper {
      .relatedMembers {
        margin-bottom: @vw30-1160;
        width: 100%;
        .relatedMember {
          .memberImage {
            width: @vw50-1160;
            height: @vw50-1160;
          }
          .innerContent {
            width: calc(100% ~"-" @vw50-1160 ~"-" @vw5-1160);
            padding-left: @vw10-1160;
          }
        }
      }
      .text {
        width: 100%;
      }
    }
    .col {
      &:first-child {
        padding-right: @vw50-1160;
        width: (@vw112-1160 * 4) + (@vw16-1160 * 5);
      }
      &:last-child {
        width: (@vw112-1160 * 3) + (@vw16-1160 * 3);
      }
    }
    .textLink {
      margin-bottom: @vw40-1160;
    }
    .imageWrapper {
      border-radius: 0 @vw100-1160 0 @vw100-1160;
    }
  }
}

@media all and (max-width: 580px) {
  .updateHeaderBlock {
    .textWrapper {
      .relatedMembers {
        margin-bottom: @vw30-580;
        width: 100%;
        .relatedMember {
          .memberImage {
            width: @vw50-580;
            height: @vw50-580;
          }
          .innerContent {
            width: calc(100% ~"-" @vw50-580 ~"-" @vw5-580);
            padding-left: @vw10-580;
          }
        }
      }
      .text {
        width: 100%;
      }
    }
    .col {
      &:first-child {
        padding-right: 0;
        width: 100%;
      }
      &:last-child {
        margin-top: @vw50-580;
        width: 100%;
      }
    }
    .textLink {
      margin-bottom: @vw40-580;
    }
    .imageWrapper {
      border-radius: 0 @vw100-580 0 @vw100-580;
      .innerImage {
        padding-bottom: 100%;
      }
    }
  }
}
