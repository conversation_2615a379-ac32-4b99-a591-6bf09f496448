.casesBlock {
  .cases {
    margin-top: @vw80;
    margin-bottom: -@vw60;
    margin-left: -@vw16 / 2;
    width: calc(100% ~"+" @vw16);
    .filters {
      display: inline-block;
      margin: 0 @vw16 / 2;
      margin-bottom: @vw60;
      padding: @vw16 + @vw112;
      position: relative;
      width: calc(33.1333% ~"-" @vw16);
      &:before {
        content: '';
        background: @secondaryColor;
        top: 0;
        left: 0;
        position: absolute;
        opacity: .2;
        height: 100%;
        width: 100%;
      }
      .subTitle {
        margin-bottom: @vw30;
      }
      .filter {
        cursor: pointer;
        display: table;
        margin-bottom: @vw10;
        .transition(.3s);
        &:hover {
          opacity: .7;
        }
        * {
          cursor: pointer;
        }
        input {
          position: absolute;
          top: 0;
          opacity: 0;
          pointer-events: none;
        }
        .checkMark {
          display: inline-block;
          height: @vw22;
          width: @vw22;
          border-radius: 0 @vw10;
          border: 2px solid @primaryColor;
          position: absolute;
          left: 0;
          vertical-align: top;
          &:after {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%,-50%);
            content: '';
            background: @thirdColor;
            opacity: 0;
            width: @vw10 - @vw2;
            height: @vw10 - @vw2;
            border-radius: 1px;
            .transition(.3s);
          }
        }
        label {
          display: inline-block;
          width: 100%;
          padding-left: @vw22 + @vw10;
          vertical-align: top;
        }
        &:last-child {
          margin-bottom: 0;
        }
        input:checked ~ .checkMark:after {
          opacity: 1;
        }
      }
    }
    .case {
      color: @primaryColor;
      text-decoration: none;
      display: inline-block;
      vertical-align: top;
      margin: 0 @vw16 / 2;
      margin-bottom: @vw60;
      width: calc(33.1333% ~"-" @vw16);
      .transition(.3s);
      &.remove {
        display: none;
      }
      &:hover {
        color: @thirdColor;
        .imageWrapper {
          .innerImage {
            img {
              transform: translate3d(0,0,0) scale(1.1);
              transition: transform .3s cubic-bezier(0.68, -0.6, 0.32, 1.6);
            }
          }
        }
        .textLink {
          color: @thirdColor;
          padding-left: @vw30;
          padding-right: 0;
          &:before {
            width: 100%;
            background: @thirdColor;
            transition-delay: .15s;
          }
          &:after {
            width: 0;
            background: @thirdColor;
          }
          i {
            color: @thirdColor;
            &:first-child {
              left: 0;
              opacity: 1;
              transition-delay: .15s;
            }
            &:last-child {
              right: -@vw10;
              opacity: 0;
              transition-delay: 0s;
            }
          }
        }
      }
      .imageWrapper {
        overflow: hidden;
        position: relative;
        height: auto;
        display: block;
        vertical-align: top;
        width: 100%;
        .innerImage {
          display: block;
          position: relative;
          height: 0;
          padding-bottom: 56.25%;
          width: 100%;
          position: relative;
          img {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            width: 100%;
            object-fit: cover;
            object-position: center;
            .transition(.3s);
            transform: translate3d(0,0,0) scale(1.05);
          }
        }
      }
      .normalTitle {
        margin: @vw20 0;
        display: block;
        display: -webkit-box;
        height: @vw44 * 3;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    .noResults {
      background: @backgroundGrey;
      padding: @vw20;
    }
  }
}

@media all and (max-width: 1160px) {
  .casesBlock {
    .cases {
      display: flex;
      margin-top: @vw80-1160;
      margin-bottom: -@vw60-1160;
      margin-left: -@vw16-1160 / 2;
      width: calc(100% ~"+" @vw16-1160);
      flex-wrap: wrap;
      .filters {
        margin: 0 @vw16-1160 / 2;
        margin-bottom: @vw60-1160;
        padding: @vw112-1160 @vw50-1160;
        width: calc(33.3333% ~"-" @vw16-1160);
        .subTitle {
          margin-bottom: @vw30-1160;
        }

        .filter {
          height: @vw22-1160;
          width: @vw22-1160;

          .checkMark {
            height: @vw22-1160;
            width: @vw22-1160;
            border-radius: 0 @vw10-1160;
            &:after {
              width: @vw10-1160 - @vw2-1160;
              height: @vw10-1160 - @vw2-1160;
              border-radius: @vw10-1160;
            }
          }

          label {
            padding-left: @vw22-1160 + @vw10-1160;
          }
        }
      }

      .case {
        margin: 0 @vw16-1160 / 2;
        margin-bottom: @vw60-1160;
        width: calc(33.3333% ~"-" @vw16-1160);
        &:hover {
          .textLink {
            padding-left: @vw30-1160;
          }
        }
        .imageWrapper {
          .innerImage {
            padding-bottom: 56.25%;

            img {
              transform: translate3d(0,0,0) scale(1.05);
            }
          }
        }

        .normalTitle {
          margin: @vw20-1160 0;
          height: @vw32-1160 * 3;
        }
      }

      .noResults {
        padding: @vw20-1160;
      }
    }
  }
}

@media all and (max-width: 580px) {
  .casesBlock {
    .cases {
      margin-top: @vw80-580;
      margin-bottom: -@vw60-580;
      margin-left: -@vw16-580 / 2;
      width: calc(100% ~"+" @vw16-580);
      .filters {
        margin: 0 @vw16-580 / 2;
        margin-bottom: @vw60-580;
        padding: @vw50-580;
        width: calc(100% ~"-" @vw16-580);
        .subTitle {
          margin-bottom: @vw30-580;
        }
        .filter {
          height: @vw22-580;
          margin-bottom: @vw22-580;
          width: @vw22-580;
          border-radius: 0 @vw22-580;
          .checkMark {
            height: @vw40-580;
            width: @vw40-580;
            border-radius: 0 @vw10-580;
            &:after {
              width: @vw20-580 - @vw2-580;
              height: @vw20-580 - @vw2-580;
              border-radius: @vw7-580;
            }
          }
          label {
            padding-top: @vw5-580;
            padding-left: @vw40-580 + @vw10-580;
          }
        }
      }
      .case {
        margin: 0 @vw16-580 / 2;
        margin-bottom: @vw60-580;
        width: calc(50% ~"-" @vw16-580);
        &:hover {
          .textLink {
            padding-left: @vw30-580;
          }
        }
        .imageWrapper {
          .innerImage {
            padding-bottom: 100%;
            img {
              transform: translate3d(0,0,0) scale(1.05);
            }
          }
        }
        .normalTitle {
          margin: @vw20-580 0;
          height: @vw32-580 * 3;
        }
      }
      .noResults {
        padding: @vw20-580;
      }
    }
  }
}
