.listBlock {
  &.inview {
    .col {
      .transition(.3s);
      opacity: 1;
      transform: translateY(0);
      &:last-child {
        transition-delay: .15s;
      }
    }
  }
  .col {
    display: inline-block;
    vertical-align: top;
    opacity: 0;
    transform: translateY(@vw20);
    &:first-child {
      padding-right: @vw112 + (@vw16 * 2);
      width: (@vw112 * 4) + (@vw16 * 4);
    }
    &:last-child {
      width: (@vw112 * 8) + (@vw16 * 7);
    }
    .normalTitle {
      margin-bottom: @vw80;
    }
    .text {
      padding-right: (@vw112 * 3) + (@vw16 * 3);
    }
    .list {
      margin-top: @vw20;
      .listItem {
        display: block;
        padding-left: @vw20 + @vw5;
        position: relative;
        &:after {
          background: @secondaryColor;
          content: '';
          position: absolute;
          left: 0;
          top: @vw5;
          width: @vw10;
          height: @vw10;
          border-radius: 0 @vw5 0 @vw5;
        }
      }
    }
  }
}

@media all and (max-width: 1160px) {
  .listBlock {
    .contentWrapper {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
    }
    .col {
      &:first-child {
        padding-right: @vw32-1160;
        width: (@vw112-1160 * 2) + (@vw16-1160 * 2);
      }
      &:last-child {
        width: (@vw112-1160 * 5) + (@vw16-1160 * 6);
      }
      .text {
        padding-right: 0;
      }
      .list {
        margin-top: @vw20-1160;
        .listItem {
          padding-left: @vw20-1160 + @vw5-1160;
          &:after {
            left: 0;
            top: @vw5-1160;
            width: @vw10-1160;
            height: @vw10-1160;
            border-radius: 0 @vw5-1160 0 @vw5-1160;
          }
        }
      }
    }
  }
}

@media all and (max-width: 580px) {
  .listBlock {
    .col {
      &:first-child {
        padding-right: 0;
        width: 100%;
      }
      &:last-child {
        width: 100%;
      }
      .list {
        margin-top: @vw20-580;
        .listItem {
          padding-left: @vw20-580 + @vw5-580;
          &:after {
            left: 0;
            top: @vw5-580;
            width: @vw10-580;
            height: @vw10-580;
            border-radius: 0 @vw5-580 0 @vw5-580;
          }
        }
      }
    }
  }
}
