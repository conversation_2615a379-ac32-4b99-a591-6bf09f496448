.callToActionsBlock {
  &.inview {
    .backgroundImage {
      opacity: 1;
      transition-delay: .15s;
    }
    .ctas {
      opacity: 1;
      transform: translateY(0);
    }
  }
  .contentWrapper {
    position: relative;
    padding-bottom: @vw60;
  }
  .backgroundImage {
    height: auto;
    width: 100%;
    position: relative;
    opacity: 0;
    .transition(.3s);
    .innerImage {
      position: relative;
      height: 0;
      padding-bottom: 43.15789473684211%;
      width: 100%;
      position: relative;
      img {
        position: absolute;
        top: 0;
        left: 0;
        object-fit: cover;
        object-position: center;
        height: 100%;
        width: 100%;
      }
    }
  }
  .ctas {
    border-radius: 0 @vw100 0 @vw100;
    position: absolute;
    top: auto;
    right: @vw50;
    bottom: 0;
    background: @primaryColor;
    padding: @vw60;
    width: (@vw112 * 7) + (@vw16 * 7);
    opacity: 0;
    .transition(.3s);
    transform: translateY(@vw20);
    &:hover {
      .cta {
        .innerLink {
          opacity: .4;
        }
      }
    }
    .cta {
      display: inline-block;
      vertical-align: top;
      width: 50%;
      &:first-child {
        padding-right: @vw20;
      }
      &:last-child {
        padding-left: @vw20;
        opacity: .6;
      }
      .innerLink {
        cursor: pointer;
        color: @secondaryColor;
        text-decoration: none;
        .transitionMore(opacity, .3s, 0s, ease-in-out);
        * {
          cursor: pointer;
        }
        &:hover {
          opacity: 1;
          .textLink {
            color: @hardWhite;
            padding-left: @vw30;
            padding-right: 0;
            &:before {
              background: @hardWhite;
              width: 100%;
            }
            &:after {
              width: 0;
              background: @hardWhite;
            }
            i {
              color: @hardWhite;
              &:first-child {
                left: 0;
                opacity: 1;
                transition-delay: .15s;
              }
              &:last-child {
                right: -@vw10;
                opacity: 0;
                transition-delay: 0s;
              }
            }
          }
        }
      }
      .normalTitle {
        margin-top: @vw20;
      }
      .textLink {
        margin-top: @vw100;
      }
    }
    span {
      display: block;
    }
  }
}

@media all and (max-width: 1160px) {
  .callToActionsBlock {
    .contentWrapper {
      padding-bottom: @vw60-1160;
    }
    .backgroundImage {
      .innerImage {
        padding-bottom: 50%;
        img {
          height: 100%;
        }
      }
    }
    .ctas {
      border-radius: 0 @vw100-1160 0 @vw100-1160;
      right: @vw50-1160;
      padding: @vw60-1160;
      width: (@vw112-1160 * 7) + (@vw16-1160 * 7);
      transform: translateY(@vw20-1160);
      &:hover {
        .cta {
          .innerLink {
            opacity: .4;
          }
        }
      }
      .cta {
        &:first-child {
          padding-right: @vw20-1160;
        }
        &:last-child {
          padding-left: @vw20-1160;
          opacity: .6;
        }
        .innerLink {
          &:hover {
            .textLink {
              color: @hardWhite;
              padding-left: @vw30-1160;
              &:before {
                background: @hardWhite;
                width: 100%;
              }
              &:after {
                background: @hardWhite;
              }
              i {
                &:first-child {
                  transition-delay: .15s;
                }
                &:last-child {
                  right: -@vw10-1160;
                }
              }
            }
          }
        }
        .normalTitle {
          margin-top: @vw20-1160;
        }
        .textLink {
          margin-top: @vw100-1160;
        }
      }
    }
  }
}

@media all and (max-width: 580px) {
  .callToActionsBlock {
    .contentWrapper {
      padding-bottom: @vw60-580;
    }
    .backgroundImage {
      .innerImage {
        padding-bottom: 50%;
        img {
          height: 100%;
        }
      }
    }
    .ctas {
      border-radius: 0 0 0 @vw100-580;
      right: 0;
      padding: @vw60-580;
      position: relative;
      width: 100%;
      transform: translate3d(0,0,0) !important;
      &:hover {
        .cta {
          .innerLink {
            opacity: .4;
          }
        }
      }
      .cta {
        display: block;
        width: 100%;
        &:first-child {
          padding-right: 0;
          margin-bottom: @vw50-580;
        }
        &:last-child {
          padding-left: 0;
          opacity: .6;
        }
        .innerLink {
          &:hover {
            .textLink {
              color: @hardWhite;
              padding-left: @vw30-580;
              &:before {
                background: @hardWhite;
                width: 100%;
              }
              &:after {
                background: @hardWhite;
              }
              i {
                &:first-child {
                  transition-delay: .15s;
                }
                &:last-child {
                  right: -@vw10-580;
                }
              }
            }
          }
        }
        .normalTitle {
          margin-top: @vw20-580;
        }
        .textLink {
          margin-top: @vw32-580;
        }
      }
    }
  }
}
