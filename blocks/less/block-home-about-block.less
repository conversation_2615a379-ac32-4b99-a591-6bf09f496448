.homeAboutBlock {
  &.inview {
    .top {
      .imageWrapper {
        transition-delay: .45s;
        opacity: 1;
        transform: translateY(0);
      }
    }
    .bottom {
      .imageWrapper {
        transition-delay: .6s;
        opacity: 1;
        transform: translateY(0);
      }
      .bottomText {
        transition-delay: .75s;
        opacity: 1;
        transform: translateY(0);
      }
    }
  }
  .top {
    .titleWrapper {
      display: inline-block;
      vertical-align: top;
      width: (@vw112 * 8) + (@vw16 * 7);
    }
    .imageWrapper {
      display: inline-block;
      vertical-align: top;
      height: auto;
      overflow: hidden;
      position: relative;
      border-radius: @vw100 * 2 0 @vw100 * 2 0;
      width: (@vw112 * 3) + (@vw16 * 2);
      margin-left: @vw112 + (@vw16 * 2);
      opacity: 0;
      .transition(.3s);
      transform: translateY(@vw20);
      .animateImage {
        height: 100%;
        width: 100%;
        top: 0;
        position: absolute;
        left: 0;
      }
      .innerImage {
        height: 0;
        padding-bottom: 130.70652173913044%;
        width: 100%;
        overflow: hidden;
        position: relative;
        img {
          position: absolute;
          top: 0%;
          left: 0;
          object-fit: cover;
          object-position: bottom;
          height: 100%;
          width: 100%;
        }
      }
    }
  }
  .bottom {
    .col {
      display: inline-block;
      vertical-align: bottom;
      width: 50%;
    }
    .imageWrapper {
      display: inline-block;
      vertical-align: bottom;
      height: auto;
      overflow: hidden;
      position: relative;
      width: (@vw112 * 6) + (@vw16 * 5);
      opacity: 0;
      .transition(.3s);
      transform: translateY(@vw20);
      .innerImage {
        position: relative;
        height: 0;
        padding-bottom: 55.71808510638298%;
        width: 100%;
        position: relative;
        img {
          position: absolute;
          top: -20%;
          left: 0;
          object-fit: cover;
          object-position: center;
          height: 120%;
          width: 100%;
        }
      }
    }
    .bottomText {
      display: inline-block;
      vertical-align: bottom;
      margin-left: @vw112 + (@vw16 * 2);
      width: (@vw112 * 3) + (@vw16 * 2);
      opacity: 0;
      .transition(.3s);
      transform: translateY(@vw20);
      .textLink {
        margin-top: @vw20;
      }
    }
  }
}
// Media Query voor schermen smaller dan 1160px
@media all and (max-width: 1160px) {
  .homeAboutBlock {
    .top {
      .titleWrapper {
        width: calc(66.6666% ~"-" @vw16-1160);
      }
      .imageWrapper {
        width: calc(33.3333% ~"-" @vw16-1160);
        margin-left: @vw32-1160;
      }
    }
    .bottom {
      .imageWrapper {
        width: 100%;
        .innerImage {
          padding-bottom: 75%;
          img {
            height: 100%;
            top: 0;
            transform: translate3D(0,0,0) !important;
          }
        }
      }
      .bottomText {
        width: 100%;
        margin-left: 0;
        padding-left: @vw16-1160;
        .textLink {
          margin-top: @vw22-1160;
        }
      }
    }
  }
}

@media all and (max-width: 580px) {
  .homeAboutBlock {
    .top {
      .titleWrapper {
        width: calc(50% ~"-" @vw16-580);
      }
      .imageWrapper {
        width: calc(50% ~"-" @vw16-580);
        margin-left: @vw32-580;
      }
    }
    .bottom {
      display: flex;
      flex-direction: column-reverse;
      flex-wrap: wrap;
      .col {
        width: 100%;
      }
      .imageWrapper {
        width: 100%;
        .innerImage {
          padding-bottom: 75%;
          img {
            height: 100%;
            top: 0;
          }
        }
      }
      .bottomText {
        margin: @vw70-580;
        width: 100%;
        margin-left: 0;
        padding-left: 0;
        .textLink {
          margin-top: @vw22-580;
        }
      }
    }
  }
}
