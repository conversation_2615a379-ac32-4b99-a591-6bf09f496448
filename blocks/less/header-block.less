.headerBlock {
  &.inview {
    .col {
      opacity: 1;
      transform: translateY(0);
      .transition(.3s);
      transition-delay: .15s;
      &:last-child {
        transition-delay: .45s;
      }
    }
  }
  &:first-child {
    margin-top: @vw100 + @vw20;
    padding-top: 0;
  }
  .col {
    display: inline-block;
    vertical-align: top;
    width: 50%;
    padding-right: (@vw112 * 2) + (@vw16 * 2);
    opacity: 0;
    transform: translateY(@vw20);
    &:last-child {
      padding-top: @vw100 + @vw10;
    }
  }
  .subTitle {
    margin-bottom: @vw20;
  }
  .textWrapper {
    display: inline-block;
    vertical-align: top;
    width: (@vw112 * 7) + (@vw16 * 7);
    .text {
      margin-top: @vw60;
      padding: 0 @vw112 + @vw16 0 (@vw112 * 2) + (@vw16 * 2)
    }
  }
  .list {
    margin-top: @vw20;
    .listItem {
      display: block;
      padding: @vw10 0;
      position: relative;
      &:after {
        content: '';
        height: 1px;
        background: @primaryColor;
        opacity: .2;
        .transition(.3s);
        width: 0%;
        bottom: 0;
        left: 0;
        position: absolute;
      }
      &.is-inview {
        &:after {
          width: 100%;
        }
      }
      i {
        color: @secondaryColor;
        padding-right: @vw16;
      }
    }
  }
  .imageWrapper {
    border-radius: 0 @vw100 0 @vw100;
    overflow: hidden;
    position: relative;
    height: auto;
    display: inline-block;
    vertical-align: top;
    width: (@vw112 * 5) + (@vw16 * 4);
    .innerImage {
      position: relative;
      height: 0;
      padding-bottom: 130.70652173913044%;
      width: 100%;
      position: relative;
      .animateImage, img {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        width: 100%;
      }
      img {
        object-fit: cover;
        object-position: bottom;
      }
    }
  }
}

@media all and (max-width: 1160px) {
  .headerBlock {
    &:first-child {
      margin-top: @vw100-1160 + @vw20-1160;
    }
    .col {
      padding-right: @vw112-1160 + @vw16-1160;
      transform: translateY(@vw20-1160);
      &:last-child {
        padding-right: 0;
        padding-top: @vw100-1160 + @vw10-1160;
      }
    }
    .textWrapper {
      width: (@vw112-1160 * 7) + (@vw16-1160 * 7);
      .text {
        padding: 0 @vw112-1160 + @vw16-1160 0 (@vw112-1160 * 2) + (@vw16-1160 * 2);
      }
    }
    .list {
      margin-top: @vw20-580;
      .listItem {
        padding: @vw10-1160 0;
        &:after {
          height: 1px;
          background: @primaryColor;
          opacity: .2;
        }
      }
    }
    .imageWrapper {
      width: (@vw112-1160 * 5) + (@vw16-1160 * 4);
      .innerImage {
        padding-bottom: 130.70652173913044%;
      }
    }
  }
}

@media all and (max-width: 580px) {
  .headerBlock {
    &:first-child {
      margin-top: @vw100-580 + @vw20-580;
    }
    .col {
      display: block;
      width: 100%;
      padding-right: 0;
      transform: translateY(0);
      &:last-child {
        margin-top: @vw32-580;
        padding-top: 0;
      }
    }
    .textWrapper {
      width: 100%;
      .text {
        padding: 0;
      }
    }
    .list {
      margin-top: @vw32-580;
      .listItem {
        padding: @vw10-580 0;
        &:after {
          height: 1px;
          background: @primaryColor;
          opacity: .2;
        }
      }
    }
    .imageWrapper {
      width: 100%;
      .innerImage {
        padding-bottom: 130.70652173913044%;
      }
    }
  }
}
