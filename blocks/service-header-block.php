<?php
$id = get_the_ID();
$image = get_field('image', $id);
$title = get_field('title', $id);
$categories = get_field('category', $id);

$usps = get_field('usp');

$the_query = new WP_Query( $usps );

$description = get_field('description', $id);
$pattern = '/(.*)\*(.*)\*(.*)/';
$replacement = '$1<span class="primary">$2</span>$3';
?>

<section class="serviceHeaderBlock secondary" data-init>
  <div class="contentWrapper">
    <div class="imageWrapper">
      <div class="innerImage">
        <span class="animateImage">
          <?php if( $image ) {?>
            <img class="lazy" data-src="<?php echo esc_url($image["sizes"]['large']); ?>" alt="<?php echo esc_attr($image['alt']); ?>" />
          <?php } ?>
        </span>
      </div>
    </div>
    <div class="textWrapper">
      <div class="titleWrapper">
        <h1 class="bigTitle primary splitThis" data-init><?php echo esc_html($title); ?></h1>
      </div>
      <div class="text">
        <?php echo($description); ?>
      </div>
      <?php if( have_rows('usp') ): ?>
        <div class="usps">
        <?php while( have_rows('usp') ) : the_row(); ?>
            <div class="usp"><?php the_sub_field('text'); ?></div>
        <?php endwhile; ?>
      </div>
      <?php endif; ?>
    </div>
  </div>
</section>
