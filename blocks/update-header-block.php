<?php
$id = get_the_ID();
$image = get_field('image', $id);
$title = get_field('title', $id);
$categories = get_field('category', $id);
$url = get_field("updates_page", $id);
$label = get_field("updates_label", $id);

$relatedMembers = get_field('related-members', $id);

$the_query = new WP_Query( $relatedMembers );

$description = get_field('description', $id);
$pattern = '/(.*)\*(.*)\*(.*)/';
$replacement = '$1<span class="primary">$2</span>$3';
?>

<section class="updateHeaderBlock" data-init>
  <div class="contentWrapper">
    <div class="col">
      <div class="textWrapper">
        <div class="subTitle secondary">
          <?php foreach( $categories as $category ): ?>
          <span class="category"><?php echo esc_html( $category->name ); ?></span>
          <?php endforeach; ?>
        </div>
        <div class="titleWrapper">
          <h1 class="bigTitle primary splitThis" data-init><?php echo esc_html($title); ?></h1>
        </div>
        <div class="relatedMembers">
          <?php if( $relatedMembers ): ?>
            <?php foreach( $relatedMembers as $post ): setup_postdata($post);
            $memberSize = 'thumbnail'; // (thumbnail, medium, large, full or custom size)
            $memberImage = get_field( 'image', $post->ID );
            $memberFunction = get_field( 'function', $post->ID );
            $memberName = get_field( 'name', $post->ID );
            $link = get_field( 'case_study', $post->ID );
          ?>
          <div class="relatedMember">
            <span class="memberImage">
              <img class="lazy" data-src="<?php echo esc_url($memberImage['sizes'][$memberSize]); ?>" alt="<?php echo esc_attr($memberImage['alt']); ?>" />
            </span>
            <span class="innerContent">
              <span class="textTitle"><?php echo esc_html( $memberName ); ?></span>
              <span class="textTitle secondary"><?php echo esc_html( $memberFunction ); ?></span>
            </span>
          </div>
          <?php endforeach; ?>
          <?php wp_reset_postdata(); ?>
          <?php endif; ?>
        </div>
        <div class="text">
          <?php echo($description); ?>
          <div class="button outline primary" data-scroll-down><i class="icon-arrow-down"></i></div>
        </div>
      </div>
    </div>
    <div class="col">
      <a class="textLink" href="<?php echo esc_html( $url ); ?>" title="<?php echo esc_html( $label ); ?>"><?php echo esc_html( $label ); ?><i class="icon-arrow-left"></i><i class="icon-arrow-left"></i></a>
      <div class="imageWrapper">
        <div class="innerImage">
          <span class="animateImage">
            <?php if( $image ) {?>
              <img class="lazy" data-src="<?php echo esc_url($image["sizes"]['large']); ?>" alt="<?php echo esc_attr($image['alt']); ?>" />
            <?php } ?>
          </span>
        </div>
      </div>
    </div>
  </div>
</section>
