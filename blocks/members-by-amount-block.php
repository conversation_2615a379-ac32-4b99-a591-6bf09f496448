<?php $backgroundColor = get_field("background_color");
$title = get_field('title');
$pattern = '/(.*)\*(.*)\*(.*)/';
$replacement = '$1<span class="primary">$2</span>$3';
$amount = get_field('amount');

$members = get_posts(array(
    'posts_per_page' => $amount,
    'post_type' => 'team-member',
    'orderby' => 'rand',
    'order'    => 'ASC'
));

$the_query = new WP_Query( $members );
$size = 'large'; // (thumbnail, medium, large, full or custom size)

?>

<section class="teamBlock <?php echo($backgroundColor); ?>" data-init>
  <div class="contentWrapper">
    <div class="cols">
      <div class="col">
        <h2 class="title"><?php echo preg_replace($pattern, $replacement, $title); ?></h2>
      </div>
      <div class="col">
        <a href="<?php the_field("link") ?>" class="button" title="<?php the_field("label") ?>"><?php the_field("label") ?></a>
      </div>
    </div>
    <div class="members">
      <?php if( $members ): ?>
        <?php foreach( $members as $post ): setup_postdata($post);
        $image = get_field( 'image', $post->ID );
        $title = get_field( 'name', $post->ID );
        $mail = get_field( 'mail', $post->ID );
        $linkedin = get_field( 'linkedin_link', $post->ID );
        $website = get_field( 'website', $post->ID );
        $function = get_field( 'function', $post->ID );
        $update = get_field( 'related_update', $post->ID );
        ?>
        <div class="member">
          <div class="imageWrapper">
            <div class="innerImage">
              <img class="lazy" data-src="<?php echo esc_url($image['sizes'][$size]); ?>" alt=<?php echo esc_url($image['alt']); ?>>
            </div>
          </div>
          <div class="innerContent">
            <div class="titleWrapper">
              <h3 class="normalTitle"><?php echo esc_html( $title ); ?></h3>
              <div class="socials">
                <?php if ($mail) { ?><a class="social" href="mailto:<?php echo esc_html( $mail ); ?>" title="Mail <?php echo esc_html( $title ); ?>"><i class="icon-envelope"></i></a><?php } ?>
                <?php if ($linkedin) { ?><a class="social" target="_blank" href="<?php echo esc_html( $linkedin ); ?>" title="Linkedin of <?php echo esc_html( $title ); ?>"><i class="icon-linkedin"></i></a><?php } ?>
                <?php if ($website) { ?><a class="social" target="_blank" href="<?php echo esc_html( $website ); ?>" title="Website of <?php echo esc_html( $title ); ?>"><i class="icon-globe"></i></a><?php } ?>
              </div>
            </div>
            <h4 class="subTitle"><?php echo(esc_html($function)); ?></h4>
            <?php if ($update) { ?>
              <a class="textLink" href="<?php echo(get_permalink($update->ID)); ?>" title="<?php echo esc_html( $update->post_title ); ?>">Bekijk gerelateerd artikel<i class="icon-arrow-right"></i><i class="icon-arrow-right"></i></a>
            <?php } ?>
          </div>
        </div>
      <?php endforeach; ?>
      <?php wp_reset_postdata(); ?>
      <?php endif; ?>
    </div>
  </div>
</section>
