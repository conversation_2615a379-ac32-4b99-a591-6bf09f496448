<?php
$id = get_the_ID();
$image = get_field('image', $id);
$title = get_field('title', $id);
$subtitle = get_field('subtitle', $id);
$description = get_field('description', $id);
$url = get_field("go_back_url", $id);
$label = get_field("go_back_label", $id);

$relatedMembers = get_field('related-members', $id);

$the_query = new WP_Query( $relatedMembers );

$description = get_field('description', $id);
$pattern = '/(.*)\*(.*)\*(.*)/';
$replacement = '$1<span class="primary">$2</span>$3';
?>

<section class="vacatureHeaderBlock" data-init>
  <div class="contentWrapper">
    <div class="top">
      <div class="col">
        <h2 class="subTitle secondary">Vacature</h2>
        <h1 class="bigTitle primary splitThis" data-init><?php echo esc_html($title); ?></h1>
      </div>
      <div class="col">
        <a class="textLink" href="<?php echo esc_html( $url ); ?>" title="<?php echo esc_html( $label ); ?>"><?php echo esc_html( $label ); ?><i class="icon-arrow-left"></i><i class="icon-arrow-left"></i></a>
      </div>
    </div>
    <div class="bottom">
      <div class="col">
        <h2 class="subTitle"><?php echo esc_html($subtitle); ?></h2>
        <div class="imageWrapper">
          <div class="innerImage">
            <span class="animateImage">
              <?php if( $image ) {?>
                <img class="lazy" data-src="<?php echo esc_url($image["sizes"]['large']); ?>" alt="<?php echo esc_attr($image['alt']); ?>" />
              <?php } ?>
            </span>
          </div>
        </div>
      </div>
      <div class="col">
        <div class="normalTitle splitThis" data-init>
          <?php echo preg_replace($pattern, $replacement, $description); ?>
        </div>
      </div>
    </div>
  </div>
</section>
