<?php
$size = 'medium'; // (thumbnail, medium, large, full or custom size)

$updates = get_posts(array(
    'posts_per_page' => -1,
    'orderby' => 'publish_date',
    'order' => 'ASC',
    'post_type'     => 'service',
));
$the_query = new WP_Query( $updates );
$title = get_field('title');
$pattern = '/(.*)\*(.*)\*(.*)/';
$replacement = '$1<span class="primary">$2</span>$3';
?>

<section class="servicesBlock" data-init>
  <div class="contentWrapper">
    <h2 class="title"><?php echo preg_replace($pattern, $replacement, $title); ?></h2>
    <div class="services">
      <?php if( $updates ): ?>
        <?php foreach( $updates as $post ): setup_postdata($post);
        $image = get_field( 'image', $post->ID );
        $title = get_field( 'title', $post->ID );
        $description = get_field( 'description', $post->ID );
        $date = get_field( 'date', $post->ID );
        $image = get_field( 'image', $post->ID );
        $categories = get_field('category', $post->ID);
        ?>
        <a class="service" href="<?php the_permalink($post->ID); ?>" title="<?php echo esc_html( $title ); ?>">
          <span class="cols">
            <span class="col">
              <span class="imageWrapper">
                <img class="lazy" data-src="<?php echo esc_url($image["sizes"]['large']); ?>" alt="<?php echo esc_attr($image['alt']); ?>" />
              </span>
            </span>
            <span class="col"><span class="normalTitle"><?php echo esc_html( $title ); ?></span></span>
            <span class="col"><span class="description"><?php echo $description; ?></span></span>
          </span>
        </a>
      <?php endforeach; ?>
      <?php wp_reset_postdata(); ?>
      <?php endif; ?>
    </div>
  </div>
</section>
