document.fonts.ready.then(function() {
  const $caseHeaderBlock = $(".caseHeaderBlock");

  if ($caseHeaderBlock.length > 0) {
    setCaseHeaderBlock();
  }

  pageContainerWrap.on('pageView', function() {
    if ($caseHeaderBlock.length > 0) {
      setCaseHeaderBlock();
    }
  });
});

function setCaseHeaderBlock() {
  const $imageWrapper = $('.caseHeaderBlock .imageWrapper');

  gsap.to($imageWrapper, {
    borderRadius: "+=20%",
    z: 0,
    scrollTrigger: {
      trigger: ".caseHeaderBlock",
      start: "middle top",
      end: "bottom top",
      scrub: true
    }
  });
}
