document.fonts.ready.then(function(){
  if ($(".casesBlock").length > 0) {
    setCasesBlock();
  }

  pageContainerWrap.on('pageView', () => {
    if ($(".casesBlock").length > 0) {
      setCasesBlock();
    }
  });
});

var currentCategories = [];
function setCasesBlock() {

  checkCheckboxes();

  $(document).on("click", ".casesBlock .filter label", function(e){
    setTimeout(function(){
      checkCheckboxes();
    },150);
    setTimeout(function(){
      filterCases();
      countItems();
    }, 450);
  });

  filterCases();
  countItems();
}

function checkCheckboxes() {
  currentCategories = [];
  $(".casesBlock .filters").find(".filter").each(function(i, el) {
    var value = $(el).find("input:checked").val();
    currentCategories.push(value);
  });
}

function filterCases() {
  $(".casesBlock .cases").find(".case").each(function(i, el){
    var categories = $(el).data("categories").split(",");
    $.each(categories, function(index, value){
      if (value != "") {
        if ($.inArray(value, currentCategories) !== -1) {
          $(el).removeClass("remove");
          return false;
        } else {
          $(el).addClass("remove");
        }
      }
    });
  });
}

function countItems() {
  var length = $(".casesBlock .cases").find(".case:visible").length;
  if (length == 0) {
    $(".casesBlock .cases").find(".noResults").show();
  } else {
    $(".casesBlock .cases").find(".noResults").hide();
  }
}
