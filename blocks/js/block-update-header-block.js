$(document).ready(function() {
  if ($(".updateHeaderBlock").length) {
    setupUpdateHeaderBlock();
  }

  pageContainerWrap.on('pageView', function() {
    if ($(".updateHeaderBlock").length) {
      setupUpdateHeaderBlock();
    }
  });
});

function setupUpdateHeaderBlock() {
  const $headerBlock = $(".updateHeaderBlock");
  gsap.killTweensOf($headerBlock.find('.imageWrapper'));

  gsap.to($headerBlock.find('.imageWrapper'), {
    borderRadius: "+=20%",
    z: 0,
    scrollTrigger: {
      trigger: $headerBlock,
      start: "middle top",
      end: "bottom top",
      scrub: true
    }
  });
}
