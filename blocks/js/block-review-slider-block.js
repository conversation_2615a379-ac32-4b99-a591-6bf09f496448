var slidesAmount = 0;
var sliderWidth = 0
var slideWidth = 0;
var activeIndex = 0;
var hammerDisabled = false;

document.fonts.ready.then(function(){
  if ($(".reviewSliderBlock").length > 0) {
    setTimeout(function(){
      setReviewSlider();
      setReviewHammer();
    }, 150);
  }

  pageContainerWrap.on('pageView', () => {
    if ($(".reviewSliderBlock").length > 0) {
      setTimeout(function(){
        setReviewSlider();
        setReviewHammer();
      }, 150);
    }
  });
});

function setReviewSlider() {
  $(".reviewSliderBlock .slide, .reviewSliderBlock .navItem").removeClass("active");
  $(".reviewSliderBlock .slide:first, .reviewSliderBlock .navItem:first").addClass("active");

  slidesAmount = $(".reviewSliderBlock .slide").length;
  slideWidth = $(".reviewSliderBlock .slide:first").outerWidth(true);
  activeIndex = $(".reviewSliderBlock .slide.active").index();
  $(document).on("click", ".reviewSliderBlock .arrowButton.prev", function(){
    activeIndex = $(".reviewSliderBlock .slide.active").index();
    if (activeIndex > 0) {
      gsap.to($(this).parents(".reviewSliderBlock").find(".innerSlider"), .3, {
        x: -slideWidth * (activeIndex-1)
      });
      $(".reviewSliderBlock .slide, .reviewSliderBlock .navItem").removeClass("active");
      $(".reviewSliderBlock .slide").eq(activeIndex-1).addClass("active");
      $(".reviewSliderBlock .navItem").eq(activeIndex-1).addClass("active");
      $(".reviewSliderBlock .arrowButton.prev").removeClass("disabled");
    }
    if (activeIndex-1 == 0) {
      $(".reviewSliderBlock .arrowButton.prev").addClass("disabled");
    }
    if (activeIndex < slidesAmount) {
      $(".reviewSliderBlock .arrowButton.next").removeClass("disabled");
    }
    setTimeout(function(){
      hammerDisabled = false;
    }, 600);
  });
  $(document).on("click", ".reviewSliderBlock .arrowButton.next", function(){
    activeIndex = $(".reviewSliderBlock .slide.active").index();
    if (activeIndex+1 < slidesAmount) {
      gsap.to($(this).parents(".reviewSliderBlock").find(".innerSlider"), .3, {
        x: -slideWidth * (activeIndex+1)
      });
      $(".reviewSliderBlock .slide, .reviewSliderBlock .navItem").removeClass("active");
      $(".reviewSliderBlock .slide").eq(activeIndex+1).addClass("active");
      $(".reviewSliderBlock .navItem").eq(activeIndex+1).addClass("active");
      $(".reviewSliderBlock .arrowButton.next").removeClass("disabled");
    }
    if (activeIndex+1 > 0) {
      $(".reviewSliderBlock .arrowButton.prev").removeClass("disabled");
    }
    if (activeIndex+3 > slidesAmount) {
      $(".reviewSliderBlock .arrowButton.next").addClass("disabled");
    }
    setTimeout(function(){
      hammerDisabled = false;
    }, 600);
  });
}

function setReviewHammer() {
  var slider = $(".reviewSliderBlock .slider")[0];
  var hammerReviewSlider = new Hammer(slider, { event: 'pan' });
  hammerReviewSlider.on('pan', function(e){
    if (!hammerDisabled) {
      if (e.additionalEvent == "panleft") {
        $(".reviewSliderBlock .arrowButton.next").trigger("click");
        hammerDisabled = true;
      }
      if (e.additionalEvent == "panright") {
        $(".reviewSliderBlock .arrowButton.prev").trigger("click");
        hammerDisabled = true;
      }
    }
  });
  $(slider).on("mousedown", function(){
    $(slider).addClass("move");
  });
  $(slider).on("mouseup", function(){
    $(slider).removeClass("move");
  });
}
