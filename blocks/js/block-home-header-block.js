document.fonts.ready.then(function() {
  const $homeHeaderBlock = $(".homeHeaderBlock");
  const $headerImage = $('.homeHeaderBlock video');

  if ($homeHeaderBlock.length > 0) {
    setBigHeaderBlock();
  }

  pageContainerWrap.on('pageView', function() {
    if ($homeHeaderBlock.length > 0) {
      setBigHeaderBlock();
    }
  });
});

function setBigHeaderBlock() {
  if ($('.homeHeaderBlock video').length > 0) {
      $headerImage = document.querySelectorAll('.homeHeaderBlock video')[0];
      ScrollTrigger.create({
        trigger: $headerImage,
        start: "top bottom",
        end: "bottom top",
        onEnter: () => {
          $headerImage.play();
        },
        onLeave: () => {
          $headerImage.pause();
        },
        onEnterBack: () => {
          $headerImage.play();
        },
        onLeaveBack: () => {
          $headerImage.pause();
        }
      });
  } else if ($('.homeHeaderBlock img').length > 0) {
      $headerImage = $('.homeHeaderBlock img');
  }

  if ($headerImage) {
    gsap.to($headerImage, {
      scale: 1.2,
      borderRadius: 0,
      ease: 'ease-in-out',
      z: 0,
      scrollTrigger: {
        trigger: ".homeHeaderBlock",
        start: "top-=200 top",
        end: 'bottom top',
        scrub: true
      }
    });
  }
}
