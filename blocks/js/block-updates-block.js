document.fonts.ready.then(function(){
  if ($(".updatesBlock").length > 0) {
    setUpdatesBlock();
  }

  pageContainerWrap.on('pageView', () => {
    if ($(".updatesBlock").length > 0) {
      setUpdatesBlock();
    }
  });
});

var currentCategory = "";
var currentYear = "";
var currentAuthor = "";

function setUpdatesBlock() {

  $(document).on("select2:select", ".customSelect", function(e){
    checkCurrentValues();
    filterUpdates();
    countItems();
  });

  checkCurrentValues();
  filterUpdates();
  countItems();
  setImageCursorUpdates();
}

function checkCurrentValues() {
  currentCategory = $(".updatesBlock .filters").find(".categoryFilter").val();
  currentYear = $(".updatesBlock .filters").find(".yearFilter").val();
  currentAuthor = $(".updatesBlock .filters").find(".authorFilter").val();
}

function filterUpdates() {
  $(".updatesBlock .updates").find(".update").each(function(i, el){
    var category = $(el).data("categories");
    var year = $(el).data("year")
    var authors = $(el).data("authors").split(",");
    var categoryBool = false;
    var yearBool = false;
    var authorsBool = false;

    if (currentCategory == "all" || currentCategory == "") {
      categoryBool = true;
    } else {
      if (currentCategory == category) {
        categoryBool = true;
      } else {
        categoryBool = false;
      }
    }

    if (currentYear == "all" || currentYear == "") {
      yearBool = true;
    } else {
      if (currentYear == year) {
        yearBool = true;
      } else {
        yearBool = false;
      }
    }

    if (currentAuthor == "all" || currentAuthor == "") {
      authorsBool = true;
    } else {
      if ($.inArray(currentAuthor, authors) !== -1) {
        authorsBool = true;
      } else {
        authorsBool = false;
      }
    }

    if (categoryBool && yearBool && authorsBool) {
      $(el).removeClass("remove");
    } else {
      $(el).addClass("remove");
    }
  });
}

function countItems() {
  var length = $(".updatesBlock .updates").find(".update:visible").length;
  if (length == 0) {
    $(".updatesBlock .updates").find(".noResults").show();
  } else {
    $(".updatesBlock .updates").find(".noResults").hide();
  }
}

function setImageCursorUpdates() {
  var imageCursor = $(".updatesBlock .imageCursor");
  var container = $(".updatesBlock .updates");
  var h = $(imageCursor).outerHeight(true);
  var w = $(imageCursor).outerWidth(true);
  var currentImage = "";
  $(document).on("mousemove", ".updatesBlock .updates", function(e){
    var relX = e.pageX - $(container).offset().left;
    var relY = e.pageY - $(container).offset().top;
    gsap.to(imageCursor, 0.3, {
      x: relX,
      y: relY,
    });
  });
  $(document).on("mouseover", "[data-mouse-image]", function(e){
    if ($(this).data("mouse-image") != currentImage) {
      $(imageCursor).find(".innerImage").html("<img src='" + $(this).data("mouse-image") + "'>");
    }
    gsap.to(imageCursor,0.4,{scale:1,autoAlpha:.2})
    currentImage = $(this).data("mouse-image");
  });
  $(document).on("mouseleave", ".updatesBlock .updates", function(e){
    gsap.to(imageCursor,0.45,{scale:.2,autoAlpha:0});
  });
}
