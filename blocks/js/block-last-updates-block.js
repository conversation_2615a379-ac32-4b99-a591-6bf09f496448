document.fonts.ready.then(function() {
  const $lastUpdatesBlock = $(".lastUpdatesBlock");

  if ($lastUpdatesBlock.length > 0) {
    setLastUpdatesBlock();
  }

  pageContainerWrap.on('pageView', function() {
    if ($lastUpdatesBlock.length > 0) {
      setLastUpdatesBlock();
    }
  });
});

function setLastUpdatesBlock() {
  const $imageCursor = $(".lastUpdatesBlock .imageCursor");
  const $container = $(".lastUpdatesBlock .updates");
  let currentImage = "";

  const updateCursorPosition = (e) => {
    const relX = e.pageX - $container.offset().left;
    const relY = e.pageY - $container.offset().top;
    gsap.to($imageCursor, {
      x: relX,
      y: relY,
      duration: 0.3
    });
  };

  const handleMouseOver = function() {
    const newImage = $(this).data("mouse-image");
    if (newImage !== currentImage) {
      $imageCursor.find(".innerImage").html(`<img src='${newImage}'>`);
      gsap.to($imageCursor, {
        scale: 1,
        autoAlpha: 0.2,
        duration: 0.4
      });
      currentImage = newImage;
    }
  };

  const handleMouseLeave = () => {
    gsap.to($imageCursor, {
      scale: 0.2,
      autoAlpha: 0,
      duration: 0.45
    });
  };

  $container.on("mousemove", updateCursorPosition);
  $(document).on("mouseover", "[data-mouse-image]", handleMouseOver);
  $container.on("mouseleave", handleMouseLeave);
}
