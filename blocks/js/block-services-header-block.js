$(document).ready(function() {
  if ($(".servicesHeaderBlock").length > 0) {
    setupHeaderBlock();
  }

  pageContainerWrap.on('pageView', function() {
    if ($(".servicesHeaderBlock").length > 0) {
      setupHeaderBlock();
    }
  });
});

function setupHeaderBlock() {
  const $headerBlock = $(".servicesHeaderBlock");

  // Clear existing GSAP animations to avoid stacking
  gsap.killTweensOf($headerBlock.find('.imageWrapper'));
  gsap.killTweensOf($headerBlock.find('.animateImg'));

  // Image Wrapper Animation
  gsap.to($headerBlock.find('.imageWrapper'), {
    borderRadius: "+=20%",
    z: 0,
    scrollTrigger: {
      trigger: $headerBlock,
      start: "middle top",
      end: "bottom top",
      scrub: true
    }
  });

  // Animated Image Animation
  gsap.to($headerBlock.find('.animateImg'), {
    scale: 1.1,
    borderRadius: 0,
    ease: 'ease-in-out',
    z: 0,
    scrollTrigger: {
      trigger: $headerBlock,
      start: "bottom bottom",
      end: "bottom top",
      scrub: true
    }
  });
}
