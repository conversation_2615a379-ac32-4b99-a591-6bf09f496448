document.fonts.ready.then(function() {
  const $aboutHeaderBlock = $(".aboutHeaderBlock");

  if ($aboutHeaderBlock.length > 0) {
    setAboutHeaderBlock();
  }

  pageContainerWrap.on('pageView', function() {
    if ($aboutHeaderBlock.length > 0) {
      setAboutHeaderBlock();
    }
  });
});

function setAboutHeaderBlock() {
  const $imageWrapper = $('.aboutHeaderBlock .imageWrapper');
  const $animateImg = $('.aboutHeaderBlock .animateImg');

  gsap.fromTo($imageWrapper,
    {
      borderRadius: '0%',
      z: 0
    },
    {
      borderRadius: "+=20%",
      z: 0,
      scrollTrigger: {
        trigger: ".aboutHeaderBlock",
        start: "middle top",
        end: 'bottom top',
        scrub: true
      }
    }
  );

  gsap.fromTo($animateImg,
    {
      scale: 1,
      borderRadius: '0%',
      z: 0
    },
    {
      scale: 1.1,
      borderRadius: '0%',
      ease: 'ease-in-out',
      z: 0,
      scrollTrigger: {
        trigger: ".aboutHeaderBlock",
        start: "bottom bottom",
        end: 'bottom top',
        scrub: true
      }
    }
  );
}
