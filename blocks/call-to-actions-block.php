<?php
  $image = get_field('image');
  $size = 'large'; // (thumbnail, medium, large, full or custom size)
?>
<section class="callToActionsBlock" data-init>
  <div class="contentWrapper">
    <div class="backgroundImage">
      <div class="innerImage">
        <img class="lazy" data-src="<?php echo esc_url($image["sizes"]['large']); ?>" alt="<?php echo esc_attr($image['alt']); ?>" />
      </div>
    </div>
    <div class="ctas" data-scroll data-scroll-speed="2">
      <div class="cta">
        <?php if( have_rows('call_to_action') ): ?>
          <?php while( have_rows('call_to_action') ): the_row(); ?>
            <a class="innerLink" href="<?php the_sub_field("link") ?>" title="<?php the_sub_field("label") ?>">
              <span class="subtitle"><?php the_sub_field("subtitle") ?></span>
              <span class="normalTitle"><?php the_sub_field("title") ?></span>
              <span class="textLink"><?php the_sub_field("label") ?><i class="icon-arrow-right"></i><i class="icon-arrow-right"></i></span>
            </a>
          <?php endwhile; ?>
        <?php endif; ?>
      </div>
      <div class="cta">
        <?php if( have_rows('call_to_action_2') ): ?>
          <?php while( have_rows('call_to_action_2') ): the_row(); ?>
            <a class="innerLink" href="<?php the_sub_field("link") ?>" title="<?php the_sub_field("label") ?>">
              <span class="subtitle"><?php the_sub_field("subtitle") ?></span>
              <span class="normalTitle"><?php the_sub_field("title") ?></span>
              <span class="textLink"><?php the_sub_field("label") ?><i class="icon-arrow-right"></i><i class="icon-arrow-right"></i></span>
            </a>
          <?php endwhile; ?>
        <?php endif; ?>
      </div>
    </div>
  </div>
</section>
