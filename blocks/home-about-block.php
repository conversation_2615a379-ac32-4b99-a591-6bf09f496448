<?php $image_left = get_field('image_left');
$image_right = get_field('image_right');
$size = 'large'; // (thumbnail, medium, large, full or custom size)
$title = get_field('title');
$pattern = '/(.*)\*(.*)\*(.*)/';
$replacement = '$1<span class="primary">$2</span>$3';
?>

<section class="homeAboutBlock" data-init>
  <div class="contentWrapper">
    <div class="top">
      <div class="titleWrapper">
        <h2 class="title splitThis" data-init><?php echo preg_replace($pattern, $replacement, $title); ?></h2>
      </div>
      <div class="imageWrapper">
        <div class="innerImage">
          <span class="animateImage">
            <?php if( $image_right ) {?>
              <img class="lazy" data-src="<?php echo esc_url($image_right["sizes"]['large']); ?>" alt="<?php echo esc_attr($image_right['alt']); ?>" />
            <?php } ?>
          </span>
        </div>
      </div>
    </div>
    <div class="bottom">
      <div class="col">
        <div class="imageWrapper">
          <div class="innerImage">
            <?php if( $image_left ) { ?>
              <img class="lazy" data-scroll data-scroll-speed="4" data-src="<?php echo esc_url($image_left['sizes']['large']); ?>" alt="<?php echo esc_attr($image_left['alt']); ?>" />
            <?php } ?>
          </div>
        </div>
      </div>

      <div class="col">
        <div class="bottomText">
          <div class="text"><?php the_field("text") ?></div>
          <?php if( have_rows('link_item') ): ?>
            <?php while( have_rows('link_item') ): the_row(); ?>
                <a class="textLink" href="<?php the_sub_field("link") ?>" title="<?php the_sub_field("label") ?>"><?php the_sub_field("label") ?><i class="icon-arrow-right"></i><i class="icon-arrow-right"></i></a>
            <?php endwhile; ?>
          <?php endif; ?>
        </div>
      </div>
    </div>
  </div>
</section>
