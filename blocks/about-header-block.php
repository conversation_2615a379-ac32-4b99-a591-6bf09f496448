<?php $image = get_field('image');
$size = 'large'; // (thumbnail, medium, large, full or custom size)
$title = get_field('title');
$pattern = '/(.*)\*(.*)\*(.*)/';
$replacement = '$1<span class="primary">$2</span>$3';
?>

<section class="aboutHeaderBlock" data-init>
  <div class="contentWrapper">
    <div class="textWrapper">
      <div class="subTitle secondary">
        <?php the_field("subtitle") ?>
      </div>
      <div class="titleWrapper">
        <h1 class="bigTitle splitThis" data-init><?php echo preg_replace($pattern, $replacement, $title); ?></h1>
      </div>
      <div class="text">
        <?php the_field("text") ?>
      </div>
    </div>
      <div class="imageWrapper">
        <div class="innerImage">
          <span class="animateImage">
            <?php if( $image ) {?>
              <img class="lazy" data-src="<?php echo esc_url($image["sizes"]['large']); ?>" alt="<?php echo esc_attr($image['alt']); ?>" />
            <?php } ?>
          </span>
        </div>
      </div>
  </div>
</section>
