<?php $backgroundColor = get_field("background_color");
$title = get_field('title');
$pattern = '/(.*)\*(.*)\*(.*)/';
$replacement = '$1<span class="primary">$2</span>$3';

$companions = get_posts(array(
  'posts_per_page' => -1,
  'post_type' => 'companion',
  'order' => 'ASC'
));

$the_query = new WP_Query( $companions );
?>

<section class="companionsBlock <?php echo($backgroundColor); ?>" data-init>
  <div class="contentWrapper">
    <h2 class="title"><?php echo preg_replace($pattern, $replacement, $title); ?></h2>
    <div class="companions">
      <?php if( $companions ): ?>
        <?php foreach( $companions as $post ): setup_postdata($post);
        $title = get_field( 'name', $post->ID );
        $mail = get_field( 'mail', $post->ID );
        $link = get_field( 'link', $post->ID );
        $website = get_field( 'website', $post->ID );
        $function = get_field( 'function', $post->ID );
        $update = get_field( 'related_update', $post->ID );
        ?>
        <?php if ($link) { ?>
          <a class="companion link" target="_blank" href="<?php echo esc_html( $link ); ?>" title="link of <?php echo esc_html( $title ); ?>">
            <span class="normalTitle"><?php echo esc_html( $title ); ?></span>
            <span class="bottom">
              <span class="social"><i class="icon-globe"></i></span>
              <span class="subTitle"><?php echo(esc_html($function)); ?></span>
            </span>
          </a>
        <?php } else { ?>
        <div class="companion">
          <h3 class="normalTitle"><?php echo esc_html( $title ); ?></h3>
          <h4 class="subTitle"><?php echo(esc_html($function)); ?></h4>
        </div>
      <?php } ?>
    <?php endforeach; ?>
    <?php wp_reset_postdata(); ?>
  <?php endif; ?>
</div>
</div>
</section>
