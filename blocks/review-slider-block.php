<?php
$size = 'thumbnail'; // (thumbnail, medium, large, full or custom size)

$reviews = get_field('reviews');
$the_query = new WP_Query( $reviews );
$title = get_field('title');
$label = get_field('label');
$pattern = '/(.*)\*(.*)\*(.*)/';
$replacement = '$1<span class="primary">$2</span>$3';
?>

<section class="reviewSliderBlock" data-init>
  <div class="contentWrapper">
    <div class="titleWrapper">
      <h2 class="title splitThis" data-init><?php echo preg_replace($pattern, $replacement, $title); ?></h2>
    </div>
    <div class="navigation">
      <div class="arrowButton prev disabled">
        <i class="icon-arrow-left"></i>
      </div>
      <div class="arrowButton next">
        <i class="icon-arrow-right"></i>
      </div>
    </div>
    <div class="slider">
      <div class="innerSlider">
          <?php if( $reviews ): ?>
            <?php foreach( $reviews as $post ): setup_postdata($post);
            $image = get_field( 'image', $post->ID );
            $reviewer = get_field( 'reviewer', $post->ID );
            $function = get_field( 'function', $post->ID );
            $quote = get_field( 'quote', $post->ID );
            $link = get_field( 'case_study', $post->ID );
            ?>
            <div class="slide">
              <div class="author">
                <?php if ($image) { ?>
                <span class="imageWrapper">
                  <img class="lazy" data-src="<?php echo esc_url($image['sizes'][$size]); ?>" alt="<?php echo esc_attr($image['alt']); ?>" />
                </span>
              <?php } ?>
                <span class="innerContent">
                  <span class="textTitle"><?php echo esc_html( $reviewer ); ?></span>
                  <span class="textTitle secondary"><?php echo esc_html( $function ); ?></span>
                </span>
              </div>
              <h3 class="normalTitle"><?php echo esc_html( $quote ); ?></h3>
              <?php if ($link) { ?>
                <a class="textLink" href="<?php echo esc_html( $link ); ?>" title="<?php echo esc_html( $label ); ?>"><?php echo esc_html( $label ); ?><i class="icon-arrow-right"></i><i class="icon-arrow-right"></i></a>
              <?php } ?>
            </div>
          <?php endforeach; ?>
          <?php wp_reset_postdata(); ?>
          <?php endif; ?>
      </div>
      <span class="sliderNavigation">
        <?php if( $reviews ): ?>
          <?php foreach( $reviews as $post ): setup_postdata($post); ?>
            <span class="navItem"></span>
          <?php endforeach; ?>
          <?php wp_reset_postdata(); ?>
        <?php endif; ?>
      </span>
    </div>
  </div>
</section>
