<?php
$id = get_the_ID();
$image = get_field('image', $id);
$title = get_field('title', $id);
$categories = get_field('category', $id);

$relatedMembers = get_field('related-members', $id);

$the_query = new WP_Query( $relatedMembers );

$description = get_field('description', $id);
$pattern = '/(.*)\*(.*)\*(.*)/';
$replacement = '$1<span class="primary">$2</span>$3';
?>

<section class="caseHeaderBlock" data-init>
  <div class="contentWrapper">
    <div class="textWrapper">
      <div class="subTitle secondary">
        <?php foreach( $categories as $category ): ?>
        <span class="category"><?php echo esc_html( $category->name ); ?></span>
        <?php endforeach; ?>
      </div>
      <div class="titleWrapper">
        <h1 class="title primary splitThis" data-init><?php echo esc_html($title); ?></h1>
      </div>
    </div>
      <div class="imageWrapper">
        <div class="innerImage">
          <span class="animateImage">
            <?php if( $image ) {?>
              <img class="lazy" data-src="<?php echo esc_url($image["sizes"]['large']); ?>" alt="<?php echo esc_attr($image['alt']); ?>" />
            <?php } ?>
          </span>
        </div>
      </div>
  </div>
</section>
