<?php
$size = 'medium'; // (thumbnail, medium, large, full or custom size)

$partners = get_posts(array(
    'posts_per_page' => -1,
    'order' => 'ASC',
    'post_type' => 'partner',
));
$the_query = new WP_Query( $partners );
?>

<section class="partnersSliderBlock" data-init>
  <div class="slider">
    <div class="innerSlider">
      <?php if( $partners ){
        $i = 1;
        while ($i < 3) { ?>
        <div class="animItem">
          <?php $index = 1;
            while ($index < 3) {
              foreach( $partners as $post ): setup_postdata($post);
                $image = get_field( 'image', $post->ID );
                $title = get_field( 'name', $post->ID );
              ?>
              <div class="item">
                <img src="<?php echo esc_url($image["sizes"]['large']); ?>" alt="<?php echo esc_attr($title); ?>" />
              </div>
            <?php endforeach;
            wp_reset_postdata();
            $index++; } ?>
        </div>
      <?php $i++; }} ?>
    </div>
  </div>
  <div class="slider right">
    <div class="innerSlider">
      <?php if( $partners ){
        $i = 1;
        while ($i < 3) { ?>
        <div class="animItem">
          <?php $index = 1;
            while ($index < 3) {
              foreach( $partners as $post ): setup_postdata($post);
                $image = get_field( 'image', $post->ID );
                $title = get_field( 'name', $post->ID );
              ?>
              <div class="item">
                <img src="<?php echo esc_url($image["sizes"]['large']); ?>" alt="<?php echo esc_attr($title); ?>" />
              </div>
            <?php endforeach;
            wp_reset_postdata();
            $index++; } ?>
        </div>
      <?php $i++; }} ?>
    </div>
  </div>
</section>
