<?php
$id = get_the_ID();
$image = get_field('image', $id);
$title = get_field('title', $id);
$categories = get_field('category', $id);

$relatedMembers = get_field('related-members', $id);
$reviews = get_field('review');
$review_query = new WP_Query( $reviews );
$the_query = new WP_Query( $relatedMembers );

$description = get_field('description', $id);
$pattern = '/(.*)\*(.*)\*(.*)/';
$replacement = '$1<span class="primary">$2</span>$3';
?>

<section class="caseFooterBlock secondary" data-init>
  <div class="contentWrapper">
    <div class="col">
      <h2 class="subTitle"><?php the_field("title") ?></h2>
      <div class="relatedMembers">
        <?php if( $relatedMembers ): ?>
          <?php foreach( $relatedMembers as $post ): setup_postdata($post);
          $memberSize = 'thumbnail'; // (thumbnail, medium, large, full or custom size)
          $memberImage = get_field( 'image', $post->ID );
          $memberFunction = get_field( 'function', $post->ID );
          $memberName = get_field( 'name', $post->ID );
        ?>
        <div class="relatedMember">
          <span class="memberImage">
            <img class="lazy" data-src="<?php echo esc_url($memberImage['sizes'][$memberSize]); ?>" alt="<?php echo esc_attr($memberImage['alt']); ?>" />
          </span>
          <span class="innerContent">
            <span class="textTitle"><?php echo esc_html( $memberName ); ?></span>
            <span class="textTitle opacity"><?php echo esc_html( $memberFunction ); ?></span>
          </span>
        </div>
        <?php endforeach; ?>
        <?php wp_reset_postdata(); ?>
        <?php endif; ?>
      </div>
    </div>
    <div class="col">
      <?php if( $reviews ): ?>
        <?php foreach( $reviews as $post ): setup_postdata($post);
        $reviewImageSize = 'thumbnail';
        $image = get_field( 'image', $post->ID );
        $reviewer = get_field( 'reviewer', $post->ID );
        $function = get_field( 'function', $post->ID );
        $quote = get_field( 'quote', $post->ID );
        $link = get_field( 'case_study', $post->ID );
        ?>
        <div class="review">
          <h3 class="normalTitle"><?php echo esc_html( $quote ); ?></h3>
          <div class="author">
            <span class="imageWrapper">
              <?php if($image){ ?>
              <img class="lazy" data-src="<?php echo esc_url($image['sizes'][$reviewImageSize]); ?>" alt="<?php echo esc_attr($image['alt']); ?>" />
              <?php } ?>
            </span>
            <span class="innerContent">
              <span class="textTitle"><?php echo esc_html( $reviewer ); ?></span>
              <span class="textTitle opacity"><?php echo esc_html( $function ); ?></span>
            </span>
          </div>
        </div>
      <?php endforeach; ?>
      <?php wp_reset_postdata(); ?>
      <?php endif; ?>
    </div>
</section>
