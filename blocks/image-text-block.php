<?php $image = get_field('image');
$size = 'large'; // (thumbnail, medium, large, full or custom size)
$title = get_field('title');
$pattern = '/(.*)\*(.*)\*(.*)/';
$replacement = '$1<span class="primary">$2</span>$3';
?>

<section class="imageTextBlock" data-init>
  <div class="contentWrapper">
    <?php if (!get_field("image_right")) { ?>
      <div class="col">
        <div class="imageWrapper" data-scroll data-scroll-speed="2">
          <?php if( $image ) {?>
            <img class="lazy" data-src="<?php echo esc_url($image["sizes"]['large']); ?>" alt="<?php echo esc_attr($image['alt']); ?>" />
          <?php } ?>
        </div>
      </div>
    <?php } ?>
    <div class="col text">
      <div class="titleWrapper">
        <h2 class="normalTitle splitThis" data-init><?php echo preg_replace($pattern, $replacement, $title); ?></h2>
      </div>
      <div class="text">
        <?php the_field("text") ?>
      </div>
    </div>
    <?php if (get_field("image_right")) { ?>
      <div class="col">
        <div class="imageWrapper" data-scroll data-scroll-speed="2">
          <?php if( $image ) {?>
            <img class="lazy" data-src="<?php echo esc_url($image["sizes"]['large']); ?>" alt="<?php echo esc_attr($image['alt']); ?>" />
          <?php } ?>
        </div>
      </div>
    <?php } ?>
  </div>
</section>
