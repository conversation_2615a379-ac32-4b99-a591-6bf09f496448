<?php
$vacatures = get_posts(array(
    'posts_per_page' => -1,
    'order' => 'ASC',
    'post_type' => 'vacature',
));
$the_query = new WP_Query( $vacatures );
$categories = array();
?>

<section class="vacaturesBlock grey" data-init>
  <div class="contentWrapper">
    <div class="vacatures">
      <?php if( $vacatures ): ?>
        <?php foreach( $vacatures as $post ): setup_postdata($post);
        $title = get_field( 'title', $post->ID );
        $categories = get_field('category', $post->ID);
        $periods = get_field('period', $post->ID);
        ?>
        <a href="<?php the_permalink($post->ID); ?>" title="<?php echo esc_html( $title ); ?>" class="vacature">
          <span class="cols">
            <span class="col">
                <?php echo esc_html($categories->name); ?>
            </span>
            <span class="col">
              <span class="col">
                  <?php echo esc_html($periods->name); ?>
              </span>
            </span>
            <span class="col"><span class="normalTitle"><?php echo esc_html( $title ); ?></span></span>
          </span>
          <i class="icon-arrow-right"></i>
        </a>
      <?php endforeach; ?>
      <?php wp_reset_postdata(); ?>
      <?php endif; ?>
      <div class="noResults" style="display:none;"><?php the_field("no_results_text") ?></div>
    </div>
  </div>
</section>
