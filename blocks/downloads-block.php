<?php $backgroundColor = get_field("background_color"); ?>

<section class="downloadsBlock <?php echo($backgroundColor); ?>" data-init>
  <div class="contentWrapper">
    <div class="col">
      <h2 class="subTitle secondary"><?php the_field("subtitle") ?></h2>
    </div>
    <div class="col">
      <?php if (get_field("title")){ ?>
        <h2 class="normalTitle">
          <?php the_field("title") ?>
        </h2>
      <?php } ?>
      <?php if (get_field("text")){ ?>
      <div class="text">
        <?php the_field("text") ?>
      </div>
    <?php } ?>

    <?php if( have_rows('download_button') ): ?>
      <div class="downloads">
      <?php while( have_rows('download_button') ) : the_row(); ?>
          <a class="button outline primary download" href="<?php the_sub_field("file") ?>" target="_blank" title="<?php the_sub_field("label") ?>">
            <i class="icon-file"></i>
            <span class="innerText"><?php the_sub_field('label'); ?></span>
            <span class="type">.<?php the_sub_field('type'); ?></span>
            <i class="icon-arrow-down"></i>
          </a>
      <?php endwhile; ?>
    </div>
    <?php endif; ?>

    </div>
  </div>
</section>
