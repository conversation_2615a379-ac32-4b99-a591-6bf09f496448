<section class="stepsBlock primary borderRadius" data-init>
  <div class="contentWrapper">
    <div class="top">
      <div class="col">
        <svg width="112.784" height="133.389" viewBox="0 0 112.784 133.389">
          <g data-name="Group 84" transform="translate(-1569.808 -664.269)">
            <path data-name="Path 42" d="M670.136,530.667l-.065,8.634-59.855-.432a24.339,24.339,0,0,0,3.864-8.612Z" transform="translate(1009.972 206.724)" fill="#9ecea2"/>
            <path data-name="Path 43" d="M598.242,563.74a18.927,18.927,0,0,0,.54,4.684L547,568.035l.065-8.634,51.631.388a17.076,17.076,0,0,0-.454,3.95" transform="translate(1022.809 200.806)" fill="#9ecea2"/>
            <path data-name="Path 44" d="M680.064,560.386,680,569.02l-22.276-.173a19.427,19.427,0,0,0,.626-4.662,18.5,18.5,0,0,0-.41-3.972Z" transform="translate(1000.326 200.641)" fill="#9ecea2"/>
            <path data-name="Path 45" d="M643.194,562.2v.077a11.171,11.171,0,0,1-11.134,11.037h-.078A11,11,0,0,1,624.138,570a10.851,10.851,0,0,1-2.394-3.68,10.984,10.984,0,0,1-.8-4.2,10.353,10.353,0,0,1,.623-3.581,11.116,11.116,0,0,1,10.492-7.474h.077a11.115,11.115,0,0,1,10.472,7.63,10.49,10.49,0,0,1,.585,3.5" transform="translate(1007.794 202.499)" fill="#9ecea2"/>
            <path data-name="Path 46" d="M593.654,519.355a15.9,15.9,0,0,1-.734,4.607v.018a15.723,15.723,0,0,1-4.956,7.287l-.019.018a15.517,15.517,0,0,1-9.893,3.542h-.111a15.289,15.289,0,0,1-9.948-3.69l-3.359-3.946v-.018a15.587,15.587,0,0,1,13.418-23.513h.128a15.605,15.605,0,0,1,15.473,15.694" transform="translate(1019.668 212.125)" fill="#9ecea2"/>
            <path data-name="Path 47" d="M601.191,591.534a9.912,9.912,0,0,1-9.886,9.822h-.086a9.916,9.916,0,0,1-9.821-9.972,9.913,9.913,0,0,1,9.886-9.8h.087a9.878,9.878,0,0,1,9.821,9.951" transform="translate(1015.824 196.302)" fill="#9ecea2"/>
            <path idata-name="Path 48" d="M655.3,489.994a47,47,0,0,1-36.8,10.792c7.339-16.577,26.55-36.133,26.55-36.133-14.484,9.109-27.132,25.967-31.881,32.766-.237.346-.454.648-.647.929a40.646,40.646,0,0,1-1.274-4.943c-3.842-18.327,4.62-32.249,14.181-41.357,10.814-10.318,26.766-14.937,42.392-12.3a5.839,5.839,0,0,1,3.281,1.339c1.123,1.079,1.339,2.741,1.425,4.23,1.014,17.117-5.073,34.083-17.225,44.681" transform="translate(1009.955 225.252)" fill="#9ecea2"/>
          </g>
        </svg>

      </div>
      <div class="col">
        <h2 class="title white">
          <?php the_field("title") ?>
        </h2>
      </div>
    </div>
    <div class="steps">
      <div class="step">
        <?php if( have_rows('step_1') ): ?>
          <?php while( have_rows('step_1') ): the_row(); ?>
            <div class="icon">
              <?php the_sub_field("icon") ?>
            </div>
            <h3 class="normalTitle"><?php the_sub_field("title") ?></h3>
            <div class="text">
              <?php the_sub_field("text") ?>
            </div>
          <?php endwhile; ?>
        <?php endif; ?>
      </div>
      <div class="step">
        <?php if( have_rows('step_2') ): ?>
          <?php while( have_rows('step_2') ): the_row(); ?>
            <div class="icon">
              <?php the_sub_field("icon") ?>
            </div>
            <h3 class="normalTitle"><?php the_sub_field("title") ?></h3>
            <div class="text">
              <?php the_sub_field("text") ?>
            </div>
          <?php endwhile; ?>
        <?php endif; ?>
      </div>
      <div class="step">
        <?php if( have_rows('step_3') ): ?>
          <?php while( have_rows('step_3') ): the_row(); ?>
            <div class="icon">
              <?php the_sub_field("icon") ?>
            </div>
            <h3 class="normalTitle"><?php the_sub_field("title") ?></h3>
            <div class="text">
              <?php the_sub_field("text") ?>
            </div>
          <?php endwhile; ?>
        <?php endif; ?>
      </div>
    </div>
  </div>
</section>
